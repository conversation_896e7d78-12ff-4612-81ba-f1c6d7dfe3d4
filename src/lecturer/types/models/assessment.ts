// Assessment-related types for the lecturer portal
// Updated to match API documentation exactly

import type { BaseEntity } from '@/shared/types/models'

// Student Information (from API)
export interface Student {
  id: number
  student_id: string
  name: string
  first_name: string
  last_name: string
  email: string
}

// Core Assessment Types (aligned with API response)
export interface Assessment extends BaseEntity {
  id: number
  name: string
  code: string
  type: string
  type_name: string
  weight: number
  due_date: string
  is_required_to_sit_final_exam: boolean
  details: AssessmentDetail[]
}

// Assessment Detail (aligned with API response)
export interface AssessmentDetail extends BaseEntity {
  id: number
  name: string
  description?: string
  weight: number
  max_points: number
  due_date: string
  score?: Score
}

// Score/Grade Types (aligned with API response)
export interface Score {
  id: number
  points_earned: number
  percentage_score: number
  letter_grade: string
  status: string
  score_status: ScoreStatus
  is_late: boolean
  minutes_late: number
  late_penalty_applied: number
  late_excuse_approved: boolean
  late_excuse_reason?: string
  bonus_points: number
  bonus_reason?: string
  score_excluded: boolean
  exclusion_reason?: string
  plagiarism_suspected: boolean
  plagiarism_score?: number
  plagiarism_notes?: string
  integrity_status: IntegrityStatus
  appeal_requested: boolean
  appeal_reason?: string
  appeal_status?: AppealStatus
  instructor_feedback?: string
  private_notes?: string
  graded_by_lecture_id?: number
  graded_at?: string
  last_modified_by_lecture_id?: number
  last_modified_at?: string
  created_at: string
  updated_at: string
}

// Legacy GradeEntry interface for backward compatibility
export interface GradeEntry extends Score {
  student_id: number
  student_name: string
  student_email: string
  assessment_detail_id: number
}

export interface RubricScore {
  criterion_id: number
  criterion_name: string
  points_earned: number
  max_points: number
  performance_level_id: number
  comments?: string
}

export interface GradeStatistics {
  assessment_id: number
  assessment_name: string
  total_students: number
  graded_count: number
  pending_count: number
  average_score: number
  median_score: number
  highest_score: number
  lowest_score: number
  standard_deviation: number
  grade_distribution: GradeDistribution[]
  completion_rate: number
  late_submission_count: number
  academic_integrity_flags: number
}

export interface GradeDistribution {
  grade_range: string
  count: number
  percentage: number
  letter_grade: string
}

// Bulk Operations Types
export interface BulkGradeUpdate {
  grade_entries: Partial<GradeEntry>[]
  operation_type: BulkOperationType
  operation_reason?: string
  batch_size?: number
  validate_before_update?: boolean
  apply_to_all?: boolean
  filters?: GradeFilter
}

export interface BulkOperationResult {
  success_count: number
  error_count: number
  errors: BulkOperationError[]
  updated_entries: GradeEntry[]
  failed_entries?: GradeEntry[]
}

export interface BulkOperationError {
  grade_id: number
  student_id?: number
  student_name?: string
  error_message: string
  error_code: string
  field?: string
}

export interface BulkOperationProgress {
  total: number
  processed: number
  successful: number
  failed: number
  errors: BulkOperationError[]
  start_time: string
  end_time?: string
  estimated_completion?: string
}

// Export Types
export interface ExportOptions {
  format: ExportFormat
  include_comments: boolean
  include_statistics: boolean
  include_rubric_scores: boolean
  include_private_notes: boolean
  date_range?: DateRange
  status_filter?: GradeStatus[]
  student_filter?: number[]
  assessment_filter?: number[]
}

export interface ExportResult {
  file_url: string
  file_name: string
  file_size: number
  expires_at: string
  download_count: number
}

// Session and State Types
export interface GradingSession {
  id: string
  course_offering_id: number
  lecturer_id: number
  mode: GradingMode
  selected_student_id?: number
  selected_assessment_id?: number
  selected_assessment_detail_id?: number
  current_page: number
  items_per_page: number
  sort_by: string
  sort_order: 'asc' | 'desc'
  filters: GradeFilter
  unsaved_changes: GradeEntry[]
  last_saved: string
  auto_save_enabled: boolean
  created_at: string
  updated_at: string
}

export interface GradeFilter {
  status?: GradeStatus[]
  is_late?: boolean
  is_excluded?: boolean
  has_academic_integrity_flag?: boolean
  score_range?: {
    min: number
    max: number
  }
  submission_date_range?: DateRange
  search_query?: string
}

// Validation Types
export interface ValidationResult {
  is_valid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  field: string
  message: string
  code: string
  value?: any
}

export interface ValidationWarning {
  field: string
  message: string
  code: string
  severity: 'low' | 'medium' | 'high'
}

export interface WeightValidation {
  total_weight: number
  is_valid: boolean
  exceeds_limit: boolean
  missing_weight: number
  component_validations: ComponentWeightValidation[]
}

export interface ComponentWeightValidation {
  assessment_id: number
  assessment_name: string
  current_weight: number
  detail_weights_sum: number
  is_valid: boolean
  errors: string[]
}

// Student Performance Types
export interface StudentPerformance {
  student_id: number
  student_name: string
  student_email: string
  overall_grade: number
  letter_grade: string
  weighted_score: number
  assessment_scores: AssessmentScore[]
  attendance_percentage: number
  participation_score?: number
  risk_level: RiskLevel
  alerts: PerformanceAlert[]
  trends: PerformanceTrend[]
  last_activity: string
}

export interface AssessmentScore {
  assessment_id: number
  assessment_name: string
  score: number
  max_score: number
  percentage: number
  weight: number
  weighted_contribution: number
  status: GradeStatus
  is_late: boolean
  submission_date?: string
}

export interface PerformanceAlert {
  id: number
  type: AlertType
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  created_at: string
  resolved_at?: string
}

export interface PerformanceTrend {
  metric: 'grade' | 'attendance' | 'participation' | 'submission_timeliness'
  direction: 'improving' | 'declining' | 'stable'
  change_percentage: number
  period: string
}

// Utility Types
export interface DateRange {
  start_date: string
  end_date: string
}

// Enums
export enum AssessmentType {
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment',
  PROJECT = 'project',
  EXAM = 'exam',
  ONLINE_ACTIVITY = 'online_activity',
  PRESENTATION = 'presentation',
  PARTICIPATION = 'participation',
  OTHER = 'other',
}

export enum SubmissionType {
  ONLINE = 'online',
  PHYSICAL = 'physical',
  BOTH = 'both',
  NONE = 'none',
}

export enum GradeStatus {
  DRAFT = 'draft',
  PROVISIONAL = 'provisional',
  FINAL = 'final',
  PENDING = 'pending',
  MISSING = 'missing',
}

export enum AcademicIntegrityStatus {
  CLEAR = 'clear',
  FLAGGED = 'flagged',
  UNDER_REVIEW = 'under_review',
  VIOLATION_CONFIRMED = 'violation_confirmed',
  APPEAL_PENDING = 'appeal_pending',
  APPEAL_APPROVED = 'appeal_approved',
  APPEAL_DENIED = 'appeal_denied',
}

export enum GradingMode {
  BY_STUDENT = 'by_student',
  BY_COMPONENT = 'by_component',
  BY_ASSESSMENT = 'by_assessment',
}

export enum BulkOperationType {
  UPDATE_GRADES = 'update_grades',
  UPDATE_STATUS = 'update_status',
  UPDATE_SCORES = 'update_scores',
  APPLY_BONUS = 'apply_bonus',
  APPLY_PENALTY = 'apply_penalty',
  EXCLUDE_SCORES = 'exclude_scores',
  UPDATE_FEEDBACK = 'update_feedback',
  MARK_LATE_EXCUSED = 'mark_late_excused',
  UPDATE_INTEGRITY_STATUS = 'update_integrity_status',
}

export enum ExportFormat {
  EXCEL = 'excel',
  PDF = 'pdf',
  CSV = 'csv',
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AlertType {
  LOW_ATTENDANCE = 'low_attendance',
  POOR_PERFORMANCE = 'poor_performance',
  MULTIPLE_LATE_SUBMISSIONS = 'multiple_late_submissions',
  ACADEMIC_INTEGRITY = 'academic_integrity',
  MISSING_SUBMISSIONS = 'missing_submissions',
  EXCEPTIONAL_PERFORMANCE = 'exceptional_performance',
}

// Type Guards
export const isValidAssessmentType = (type: string): type is AssessmentType => {
  return Object.values(AssessmentType).includes(type as AssessmentType)
}

export const isValidGradeStatus = (status: string): status is GradeStatus => {
  return Object.values(GradeStatus).includes(status as GradeStatus)
}

export const isValidGradingMode = (mode: string): mode is GradingMode => {
  return Object.values(GradingMode).includes(mode as GradingMode)
}

// Helper Types for API Requests
export interface CreateAssessmentRequest {
  course_offering_id: number
  name: string
  type: AssessmentType
  weight: number
  max_points: number
  due_date?: string
  is_required: boolean
  description?: string
  instructions?: string
  submission_type: SubmissionType
  late_penalty_per_day?: number
  max_late_days?: number
  allow_resubmission: boolean
  details: CreateAssessmentDetailRequest[]
}

export interface CreateAssessmentDetailRequest {
  name: string
  weight: number
  max_points: number
  description?: string
  order_index: number
}

export interface UpdateAssessmentRequest extends Partial<CreateAssessmentRequest> {
  id: number
}

export interface UpdateGradeRequest {
  id: number
  points_earned?: number
  status?: GradeStatus
  feedback?: string
  private_notes?: string
  bonus_points?: number
  bonus_reason?: string
  is_excluded?: boolean
  exclusion_reason?: string
  academic_integrity?: AcademicIntegrityStatus
  plagiarism_score?: number
  plagiarism_notes?: string
  rubric_scores?: RubricScore[]
}

export interface GradeQueryParams {
  course_offering_id: number
  assessment_id?: number
  student_id?: number
  status?: GradeStatus[]
  page?: number
  per_page?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  include_statistics?: boolean
}
