<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/shared/components/ui/tabs'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import OverviewStatisticsCard from './OverviewStatisticsCard.vue'
import GradeMatrixDisplay from './GradeMatrixDisplay.vue'
import { useAssessmentStore } from '@/lecturer/stores/assessment'
import { storeToRefs } from 'pinia'
import type { 
  OverviewStatistics, 
  GradeMatrix, 
  DetailedStatistics,
  ExcelExportFilters,
  PdfExportFilters,
  GradeMatrixFilters
} from '@/lecturer/types/models/assessment'
import {
  BarChart3,
  FileText,
  Download,
  RefreshCw,
  AlertTriangle,
  TrendingUp,
  Users,
  Target,
} from 'lucide-vue-next'

interface Props {
  courseOfferingId: number
}

const props = defineProps<Props>()

// Store
const assessmentStore = useAssessmentStore()
const {
  overviewStatistics,
  gradeMatrix,
  detailedStatistics,
  loading,
  error,
  exportLoading,
} = storeToRefs(assessmentStore)

// Local state
const activeTab = ref('overview')
const gradeMatrixFilters = ref<GradeMatrixFilters>({
  include_excluded: false,
  score_status: '',
  student_ids: [],
  component_ids: [],
})

// Computed properties
const hasOverviewData = computed(() => !!overviewStatistics.value)
const hasGradeMatrixData = computed(() => !!gradeMatrix.value)
const hasDetailedData = computed(() => !!detailedStatistics.value)

// Methods
const loadOverviewStatistics = async () => {
  try {
    await assessmentStore.loadOverviewStatistics(props.courseOfferingId)
  } catch (error) {
    console.error('Failed to load overview statistics:', error)
  }
}

const loadGradeMatrix = async (filters?: GradeMatrixFilters) => {
  try {
    await assessmentStore.loadGradeMatrix(props.courseOfferingId, filters)
  } catch (error) {
    console.error('Failed to load grade matrix:', error)
  }
}

const handleGradeMatrixFiltersUpdate = (filters: GradeMatrixFilters) => {
  gradeMatrixFilters.value = filters
  loadGradeMatrix(filters)
}

const handleRefreshOverview = () => {
  loadOverviewStatistics()
}

const handleRefreshGradeMatrix = () => {
  loadGradeMatrix(gradeMatrixFilters.value)
}

const handleExportExcel = async (filters?: ExcelExportFilters) => {
  try {
    await assessmentStore.exportToExcel(props.courseOfferingId, filters)
  } catch (error) {
    console.error('Failed to export to Excel:', error)
  }
}

const handleExportPdf = async (filters?: PdfExportFilters) => {
  try {
    await assessmentStore.exportToPdf(props.courseOfferingId, filters)
  } catch (error) {
    console.error('Failed to export to PDF:', error)
  }
}

const handleGradeMatrixExport = (format: 'excel' | 'pdf') => {
  const filters = {
    ...gradeMatrixFilters.value,
    include_grade_matrix: true,
    include_statistics: true,
  }
  
  if (format === 'excel') {
    handleExportExcel(filters as ExcelExportFilters)
  } else {
    handleExportPdf(filters as PdfExportFilters)
  }
}

const refreshAllData = async () => {
  await Promise.all([
    loadOverviewStatistics(),
    loadGradeMatrix(gradeMatrixFilters.value),
  ])
}

// Lifecycle
onMounted(() => {
  refreshAllData()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold tracking-tight">Assessment Reports</h2>
        <p class="text-muted-foreground">
          Comprehensive analytics and reporting for course assessments
        </p>
      </div>
      
      <div class="flex items-center gap-2">
        <Button variant="outline" @click="refreshAllData" :disabled="loading">
          <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" />
          Refresh All
        </Button>
        
        <Button @click="handleExportExcel()" :disabled="exportLoading || !hasOverviewData">
          <Download class="h-4 w-4 mr-2" />
          Export Excel
        </Button>
        
        <Button variant="outline" @click="handleExportPdf()" :disabled="exportLoading || !hasOverviewData">
          <Download class="h-4 w-4 mr-2" />
          Export PDF
        </Button>
      </div>
    </div>

    <!-- Global Error Alert -->
    <Alert v-if="error && !loading" variant="destructive">
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription>
        {{ error }}
      </AlertDescription>
    </Alert>

    <!-- Main Content Tabs -->
    <Tabs v-model="activeTab" class="space-y-4">
      <TabsList class="grid w-full grid-cols-3">
        <TabsTrigger value="overview" class="flex items-center gap-2">
          <BarChart3 class="h-4 w-4" />
          Overview
        </TabsTrigger>
        <TabsTrigger value="matrix" class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          Grade Matrix
        </TabsTrigger>
        <TabsTrigger value="detailed" class="flex items-center gap-2">
          <TrendingUp class="h-4 w-4" />
          Detailed Stats
        </TabsTrigger>
      </TabsList>

      <!-- Overview Tab -->
      <TabsContent value="overview" class="space-y-4">
        <OverviewStatisticsCard
          :data="overviewStatistics"
          :loading="loading"
          :error="error"
        />
        
        <!-- Course Information -->
        <Card v-if="overviewStatistics">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Users class="h-5 w-5" />
              Course Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid gap-4 md:grid-cols-2">
              <div>
                <h4 class="font-medium">{{ overviewStatistics.course_offering.course_code }}</h4>
                <p class="text-sm text-muted-foreground">{{ overviewStatistics.course_offering.course_title }}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  Section: {{ overviewStatistics.course_offering.section_code }}
                </p>
              </div>
              <div>
                <h4 class="font-medium">{{ overviewStatistics.course_offering.semester.name }} {{ overviewStatistics.course_offering.semester.year }}</h4>
                <p class="text-sm text-muted-foreground">Instructor: {{ overviewStatistics.course_offering.instructor.name }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Grade Matrix Tab -->
      <TabsContent value="matrix" class="space-y-4">
        <GradeMatrixDisplay
          :data="gradeMatrix"
          :loading="loading"
          :error="error"
          :filters="gradeMatrixFilters"
          @update-filters="handleGradeMatrixFiltersUpdate"
          @refresh="handleRefreshGradeMatrix"
          @export="handleGradeMatrixExport"
        />
      </TabsContent>

      <!-- Detailed Statistics Tab -->
      <TabsContent value="detailed" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <TrendingUp class="h-5 w-5" />
              Detailed Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div v-if="loading" class="flex items-center justify-center py-8">
              <div class="text-center">
                <RefreshCw class="h-8 w-8 animate-spin mx-auto mb-2" />
                <p class="text-muted-foreground">Loading detailed statistics...</p>
              </div>
            </div>
            
            <div v-else-if="hasDetailedData" class="space-y-4">
              <div class="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 class="font-medium mb-2">Course: {{ detailedStatistics?.course_offering.course_code }}</h4>
                  <p class="text-sm text-muted-foreground">{{ detailedStatistics?.course_offering.course_title }}</p>
                </div>
                <div>
                  <h4 class="font-medium mb-2">Generated</h4>
                  <p class="text-sm text-muted-foreground">{{ detailedStatistics?.generated_at }}</p>
                </div>
              </div>
              
              <!-- Statistics would be displayed here based on the actual data structure -->
              <div class="text-sm text-muted-foreground">
                Detailed statistics data structure varies based on the type parameter.
                Implementation would depend on the specific statistics returned by the API.
              </div>
            </div>
            
            <div v-else class="text-center py-8">
              <Target class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-muted-foreground">No detailed statistics available</p>
              <Button variant="outline" class="mt-4" @click="loadOverviewStatistics">
                Load Statistics
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
