<script setup lang="ts">
import { ref, computed, onErrorCaptured } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shared/components/ui/collapsible'
import { useErrorHandler, type ApiError, type ErrorContext } from '@/lecturer/utils/apiErrorHandler'
import {
  AlertTriangle,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Bug,
  Wifi,
  Clock,
  Shield,
  Info,
} from 'lucide-vue-next'

interface Props {
  title?: string
  showDetails?: boolean
  allowRetry?: boolean
  context?: ErrorContext
}

interface Emits {
  (e: 'retry'): void
  (e: 'error', error: ApiError): void
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Something went wrong',
  showDetails: false,
  allowRetry: true,
})

const emit = defineEmits<Emits>()

// Error handling
const { handleError, isRecoverableError } = useErrorHandler()

// Local state
const error = ref<ApiError | null>(null)
const showErrorDetails = ref(false)
const retryCount = ref(0)
const isRetrying = ref(false)

// Computed properties
const errorIcon = computed(() => {
  if (!error.value) return AlertTriangle
  
  switch (error.value.code) {
    case 'NETWORK_ERROR':
      return Wifi
    case 'REQUEST_TIMEOUT':
    case 'EXPORT_TIMEOUT':
      return Clock
    case 'UNAUTHORIZED':
    case 'FORBIDDEN':
      return Shield
    default:
      return AlertTriangle
  }
})

const errorVariant = computed(() => {
  if (!error.value) return 'destructive'
  
  switch (error.value.code) {
    case 'NETWORK_ERROR':
    case 'REQUEST_TIMEOUT':
    case 'SERVICE_UNAVAILABLE':
      return 'default'
    case 'VALIDATION_FAILED':
    case 'INVALID_INPUT':
      return 'default'
    default:
      return 'destructive'
  }
})

const canRetry = computed(() => {
  return props.allowRetry && 
         error.value && 
         isRecoverableError(error.value) && 
         retryCount.value < 3
})

const errorTitle = computed(() => {
  if (!error.value) return props.title
  
  const titles: Record<string, string> = {
    'NETWORK_ERROR': 'Connection Problem',
    'REQUEST_TIMEOUT': 'Request Timed Out',
    'SERVICE_UNAVAILABLE': 'Service Temporarily Unavailable',
    'UNAUTHORIZED': 'Authentication Required',
    'FORBIDDEN': 'Access Denied',
    'VALIDATION_FAILED': 'Invalid Data',
    'NOT_FOUND': 'Resource Not Found',
    'RATE_LIMIT_EXCEEDED': 'Too Many Requests',
  }
  
  return titles[error.value.code] || props.title
})

// Methods
const handleRetry = async () => {
  if (!canRetry.value) return
  
  isRetrying.value = true
  retryCount.value++
  
  try {
    emit('retry')
    // Clear error if retry is initiated
    setTimeout(() => {
      if (isRetrying.value) {
        error.value = null
        isRetrying.value = false
      }
    }, 1000)
  } catch (err) {
    isRetrying.value = false
    // Error will be caught by onErrorCaptured
  }
}

const clearError = () => {
  error.value = null
  retryCount.value = 0
  showErrorDetails.value = false
}

const reportError = () => {
  if (error.value) {
    // In a real app, this would send the error to a reporting service
    console.log('Reporting error:', error.value)
    
    // For now, just copy to clipboard
    const errorInfo = {
      error: error.value,
      context: props.context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }
    
    navigator.clipboard?.writeText(JSON.stringify(errorInfo, null, 2))
      .then(() => {
        // Could show a toast notification here
        console.log('Error details copied to clipboard')
      })
      .catch(console.error)
  }
}

// Error capture
onErrorCaptured((err: any) => {
  const result = handleError(err, props.context)
  error.value = result.apiError
  emit('error', result.apiError)
  
  // Return false to prevent the error from propagating further
  return false
})

// Expose methods for parent components
defineExpose({
  setError: (err: any) => {
    const result = handleError(err, props.context)
    error.value = result.apiError
    emit('error', result.apiError)
  },
  clearError,
  retry: handleRetry,
})
</script>

<template>
  <div>
    <!-- Error Display -->
    <Alert v-if="error" :variant="errorVariant" class="mb-4">
      <component :is="errorIcon" class="h-4 w-4" />
      <AlertDescription>
        <div class="space-y-3">
          <!-- Error Title and Message -->
          <div>
            <h4 class="font-medium">{{ errorTitle }}</h4>
            <p class="text-sm mt-1">
              {{ error.message }}
            </p>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex items-center gap-2">
            <Button
              v-if="canRetry"
              variant="outline"
              size="sm"
              @click="handleRetry"
              :disabled="isRetrying"
              class="gap-2"
            >
              <RefreshCw class="h-3 w-3" :class="{ 'animate-spin': isRetrying }" />
              {{ isRetrying ? 'Retrying...' : 'Try Again' }}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              @click="clearError"
              class="gap-2"
            >
              Dismiss
            </Button>
            
            <Button
              v-if="showDetails"
              variant="ghost"
              size="sm"
              @click="reportError"
              class="gap-2"
            >
              <Bug class="h-3 w-3" />
              Report Issue
            </Button>
          </div>
          
          <!-- Retry Information -->
          <div v-if="retryCount > 0" class="text-xs text-muted-foreground">
            Retry attempt {{ retryCount }} of 3
          </div>
          
          <!-- Error Details (Collapsible) -->
          <Collapsible v-if="showDetails">
            <CollapsibleTrigger
              @click="showErrorDetails = !showErrorDetails"
              class="flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground"
            >
              <component
                :is="showErrorDetails ? ChevronDown : ChevronRight"
                class="h-3 w-3"
              />
              Technical Details
            </CollapsibleTrigger>
            <CollapsibleContent class="mt-2">
              <Card class="bg-muted/50">
                <CardContent class="p-3">
                  <div class="space-y-2 text-xs font-mono">
                    <div>
                      <span class="font-semibold">Code:</span> {{ error.code }}
                    </div>
                    <div v-if="error.statusCode">
                      <span class="font-semibold">Status:</span> {{ error.statusCode }}
                    </div>
                    <div v-if="error.timestamp">
                      <span class="font-semibold">Time:</span> {{ error.timestamp }}
                    </div>
                    <div v-if="error.details">
                      <span class="font-semibold">Details:</span>
                      <pre class="mt-1 text-xs bg-background p-2 rounded border overflow-auto max-h-32">{{ JSON.stringify(error.details, null, 2) }}</pre>
                    </div>
                    <div v-if="context">
                      <span class="font-semibold">Context:</span>
                      <pre class="mt-1 text-xs bg-background p-2 rounded border overflow-auto max-h-32">{{ JSON.stringify(context, null, 2) }}</pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </AlertDescription>
    </Alert>
    
    <!-- Slot content (rendered when no error) -->
    <div v-if="!error">
      <slot />
    </div>
    
    <!-- Fallback content when error occurs -->
    <div v-else-if="$slots.fallback">
      <slot name="fallback" :error="error" :retry="handleRetry" :clear="clearError" />
    </div>
  </div>
</template>

<style scoped>
/* Ensure proper spacing for error details */
pre {
  white-space: pre-wrap;
  word-break: break-word;
}

/* Animation for retry button */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
