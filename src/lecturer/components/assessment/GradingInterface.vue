<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/shared/components/ui/tabs'
import { Badge } from '@/shared/components/ui/badge'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Separator } from '@/shared/components/ui/separator'
import { Label } from '@/shared/components/ui/label'
import { Checkbox } from '@/shared/components/ui/checkbox'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import GradingTable from './GradingTable.vue'
import ErrorBoundary from './ErrorBoundary.vue'
import { useGrading } from '@/lecturer/composables/useGrading'
import {
  useKeyboardShortcuts,
  createGradingShortcuts,
  useFocusManagement,
} from '@/lecturer/composables/useKeyboardShortcuts'
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp.vue'
import { useErrorHandler } from '@/lecturer/utils/apiErrorHandler'
import { GradingMode, GradeStatus } from '@/lecturer/types/models/assessment'
import type { GradeEntry, UpdateGradeRequest } from '@/lecturer/types/models/assessment'
import {
  Users,
  ClipboardList,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  FileText,
  Keyboard,
  Info,
  Wifi,
  WifiOff,
  Shield,
  ShieldAlert,
} from 'lucide-vue-next'

interface Props {
  courseId: string
  assessmentId?: string
}

const props = defineProps<Props>()

// Use the useGrading composable for state management
const {
  // New API data
  studentGradingData,
  componentGradingData,

  // Legacy data for backward compatibility
  grades,
  students,
  currentMode,
  selectedStudentId,
  selectedAssessmentId,
  selectedAssessmentDetailId,
  loading,
  error,
  hasUnsavedChanges,
  filteredGrades,
  paginatedGrades,
  totalPages,
  currentStudent,
  studentGrades,
  gradesByAssessmentDetail,
  isSaving,
  lastAutoSave,

  // New API methods
  loadStudentData,
  loadComponentData,
  updateGradeReal,
  bulkUpdateGradesReal,

  // Legacy methods for backward compatibility
  updateGrade,
  bulkUpdateGrades,
  switchMode,
  selectStudent,
  selectNextStudent,
  selectPreviousStudent,
  selectAssessment,
  selectAssessmentDetail,
  updateFilters,
  updateSort,
  clearFilters,
  goToPage,
  setItemsPerPage,
  saveAllChanges,
  refreshData,
  getGradeById,
  getGradesByStudent,
  hasValidationErrors,
  getValidationErrors,
} = useGrading(parseInt(props.courseId), props.assessmentId ? parseInt(props.assessmentId) : undefined)

// Keyboard shortcuts and focus management
const keyboardShortcuts = useKeyboardShortcuts()
const focusManagement = useFocusManagement()

// Error handling
const { handleError } = useErrorHandler()
const errorBoundaryRef = ref<InstanceType<typeof ErrorBoundary> | null>(null)

// Local state
const activeTab = ref<GradingMode>(GradingMode.BY_STUDENT)
const showKeyboardShortcuts = ref(false)
const isAutoSaveEnabled = ref(true)
const validationErrors = ref<Map<number, any>>(new Map())
const conflicts = ref<any[]>([])

// Sync activeTab with currentMode from composable
watch(
  currentMode,
  (newMode) => {
    activeTab.value = newMode
  },
  { immediate: true },
)

// Computed properties
const gradingModeOptions = [
  {
    value: GradingMode.BY_STUDENT,
    label: 'By Student',
    icon: Users,
    description: 'Grade all assessments for each student',
    ariaLabel: 'Switch to grade by student mode',
  },
  {
    value: GradingMode.BY_COMPONENT,
    label: 'By Component',
    icon: ClipboardList,
    description: 'Grade one assessment component across all students',
    ariaLabel: 'Switch to grade by component mode',
  },
]

const statusCounts = computed(() => {
  const counts = {
    [GradeStatus.FINAL]: 0,
    [GradeStatus.PROVISIONAL]: 0,
    [GradeStatus.DRAFT]: 0,
    [GradeStatus.PENDING]: 0,
    [GradeStatus.MISSING]: 0,
  }

  grades.value.forEach((grade) => {
    counts[grade.status]++
  })

  return counts
})

const gradingProgress = computed(() => {
  const total = grades.value.length
  const completed = grades.value.filter(
    (grade) => grade.status === GradeStatus.FINAL || grade.status === GradeStatus.PROVISIONAL,
  ).length

  return {
    total,
    completed,
    percentage: total > 0 ? Math.round((completed / total) * 100) : 0,
  }
})

const saveStatusMessage = computed(() => {
  if (isSaving.value) {
    return 'Saving changes...'
  }
  if (hasUnsavedChanges.value) {
    return 'You have unsaved changes'
  }
  if (lastAutoSave.value) {
    const savedTime = new Date(lastAutoSave.value).toLocaleTimeString()
    return `Last saved at ${savedTime}`
  }
  return 'All changes saved'
})

const validationStatusMessage = computed(() => {
  const errorCount = validationErrors.value.size
  if (errorCount > 0) {
    return `${errorCount} validation errors found`
  }
  return 'All validations passed'
})

const connectionStatus = computed(() => {
  if (conflicts.value.length > 0) {
    return 'resolving-conflicts'
  }
  if (error.value) {
    return 'error'
  }
  if (isSaving.value) {
    return 'syncing'
  }
  return 'connected'
})

const hasValidationErrorsComputed = computed(() => validationErrors.value.size > 0)
const hasValidationWarnings = computed(() => false) // Simplified for now
const totalValidationIssues = computed(() => validationErrors.value.size)

// Keyboard shortcuts setup
const setupKeyboardShortcuts = () => {
  const shortcuts = createGradingShortcuts({
    save: () => {
      handleSaveAll()
      announceToScreenReader('Save shortcut activated')
    },
    undo: () => {
      handleDiscardChanges()
      announceToScreenReader('Undo shortcut activated - changes discarded')
    },
    switchToByStudent: () => {
      handleModeChange(GradingMode.BY_STUDENT)
    },
    switchToByComponent: () => {
      handleModeChange(GradingMode.BY_COMPONENT)
    },
    showHelp: () => {
      keyboardShortcuts.toggleHelp()
      announceToScreenReader('Keyboard shortcuts help toggled')
    },
    closeModal: () => {
      if (showKeyboardShortcuts.value) {
        showKeyboardShortcuts.value = false
        announceToScreenReader('Help modal closed')
      }
    },
    focusSearch: () => {
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        announceToScreenReader('Search input focused')
      }
    },
    nextStudent: () => {
      if (currentMode.value === GradingMode.BY_STUDENT) {
        selectNextStudent()
        announceToScreenReader(`Next student selected: ${currentStudent.value?.name || 'Unknown'}`)
      }
    },
    previousStudent: () => {
      if (currentMode.value === GradingMode.BY_STUDENT) {
        selectPreviousStudent()
        announceToScreenReader(`Previous student selected: ${currentStudent.value?.name || 'Unknown'}`)
      }
    },
  })

  shortcuts.forEach((shortcut) => {
    keyboardShortcuts.registerShortcut(shortcut)
  })
}

// Methods
const handleModeChange = async (mode: string | number) => {
  const newMode = mode as GradingMode
  activeTab.value = newMode
  switchMode(newMode)

  // Announce mode change to screen readers
  announceToScreenReader(
    `Switched to ${newMode === GradingMode.BY_STUDENT ? 'grade by student' : 'grade by component'} mode`,
  )
}

const handleSaveAll = async () => {
  if (!hasUnsavedChanges.value || hasValidationErrorsComputed.value) return

  try {
    const success = await saveAllChanges()
    if (success) {
      announceToScreenReader('All changes saved successfully')
    } else {
      announceToScreenReader('Failed to save changes. Please check for validation errors.')
    }
  } catch (error) {
    console.error('Failed to save changes:', error)
    announceToScreenReader('Failed to save changes. Please try again.')
  }
}

const handleDiscardChanges = () => {
  // Clear unsaved changes by refreshing data
  refreshData()
  announceToScreenReader('All unsaved changes discarded')
}

// New API Methods
const handleLoadStudentData = async (studentId: number) => {
  try {
    await loadStudentData(studentId)
    announceToScreenReader(`Student grading data loaded for student ID ${studentId}`)
  } catch (error) {
    const errorResult = handleError(error, {
      operation: 'loadStudentData',
      resource: 'student_grading_data',
      userId: studentId,
      courseId: parseInt(props.courseId),
      timestamp: new Date(),
    })

    errorBoundaryRef.value?.setError(error)
    announceToScreenReader(`Failed to load student grading data: ${errorResult.userMessage}`)
  }
}

const handleLoadComponentData = async (assessmentComponentId: number) => {
  try {
    await loadComponentData(assessmentComponentId)
    announceToScreenReader(`Component grading data loaded for assessment component ID ${assessmentComponentId}`)
  } catch (error) {
    const errorResult = handleError(error, {
      operation: 'loadComponentData',
      resource: 'component_grading_data',
      courseId: parseInt(props.courseId),
      timestamp: new Date(),
    })

    errorBoundaryRef.value?.setError(error)
    announceToScreenReader(`Failed to load component grading data: ${errorResult.userMessage}`)
  }
}

const handleUpdateGradeReal = async (scoreId: number, gradeData: any) => {
  try {
    await updateGradeReal(scoreId, gradeData)
    announceToScreenReader('Grade updated successfully')
  } catch (error) {
    const errorResult = handleError(error, {
      operation: 'updateGrade',
      resource: 'score',
      courseId: parseInt(props.courseId),
      timestamp: new Date(),
    })

    errorBoundaryRef.value?.setError(error)
    announceToScreenReader(`Failed to update grade: ${errorResult.userMessage}`)
  }
}

const handleBulkUpdateGradesReal = async (bulkData: any) => {
  try {
    await bulkUpdateGradesReal(bulkData)
    announceToScreenReader('Bulk grade update completed successfully')
  } catch (error) {
    const errorResult = handleError(error, {
      operation: 'bulkUpdateGrades',
      resource: 'scores',
      courseId: parseInt(props.courseId),
      timestamp: new Date(),
    })

    errorBoundaryRef.value?.setError(error)
    announceToScreenReader(`Failed to bulk update grades: ${errorResult.userMessage}`)
  }
}

// Legacy Methods (for backward compatibility)
const handleGradeChange = async (gradeId: number, updates: Partial<UpdateGradeRequest>) => {
  try {
    const success = await updateGrade(gradeId, updates)
    if (!success) {
      // Handle validation errors
      const errors = getValidationErrors(gradeId)
      if (errors) {
        validationErrors.value.set(gradeId, errors)
      }
    } else {
      // Clear validation errors for this grade
      validationErrors.value.delete(gradeId)
    }
  } catch (error) {
    console.error('Failed to update grade:', error)
  }
}

const toggleAutoSave = () => {
  isAutoSaveEnabled.value = !isAutoSaveEnabled.value
  announceToScreenReader(`Auto-save ${isAutoSaveEnabled.value ? 'enabled' : 'disabled'}`)
}

const announceToScreenReader = (message: string, urgent = false) => {
  const regionId = urgent ? 'sr-urgent-announcements' : 'sr-announcements'
  const region = document.getElementById(regionId)

  if (region) {
    region.textContent = message

    // Clear the message after a delay to allow for new announcements
    setTimeout(() => {
      if (region.textContent === message) {
        region.textContent = ''
      }
    }, 1000)
  }
}

// Enhanced keyboard navigation for grading table
const setupTableNavigation = () => {
  // Add keyboard event listeners for enhanced navigation
  const handleKeyDown = (event: KeyboardEvent) => {
    // Handle global keyboard shortcuts that should work anywhere in the interface
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 's':
          event.preventDefault()
          handleSaveAll()
          break
        case 'z':
          event.preventDefault()
          handleDiscardChanges()
          break
        case '1':
          event.preventDefault()
          handleModeChange(GradingMode.BY_STUDENT)
          break
        case '2':
          event.preventDefault()
          handleModeChange(GradingMode.BY_COMPONENT)
          break
        case '/':
          event.preventDefault()
          const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
          if (searchInput) {
            searchInput.focus()
          }
          break
      }
    }

    // Handle arrow key navigation for student selection in BY_STUDENT mode
    if (currentMode.value === GradingMode.BY_STUDENT && !event.ctrlKey && !event.metaKey) {
      switch (event.key) {
        case 'ArrowUp':
          if (event.altKey) {
            event.preventDefault()
            selectPreviousStudent()
          }
          break
        case 'ArrowDown':
          if (event.altKey) {
            event.preventDefault()
            selectNextStudent()
          }
          break
      }
    }
  }

  document.addEventListener('keydown', handleKeyDown)

  // Return cleanup function
  return () => {
    document.removeEventListener('keydown', handleKeyDown)
  }
}

// Store cleanup function
let cleanupTableNavigation: (() => void) | null = null

// Lifecycle hooks
onMounted(async () => {
  // Set up keyboard shortcuts
  setupKeyboardShortcuts()

  // Set up table navigation and store cleanup function
  cleanupTableNavigation = setupTableNavigation()

  // Set initial focus for accessibility
  nextTick(() => {
    const firstFocusable = focusManagement.getFirstFocusableElement(
      document.querySelector('[data-grading-interface]') as HTMLElement,
    )
    if (firstFocusable) {
      firstFocusable.focus()
    }
  })

  // Announce interface ready to screen readers
  announceToScreenReader('Assessment grading interface loaded and ready')
})

onUnmounted(() => {
  keyboardShortcuts.clearShortcuts()

  // Clean up table navigation event listeners
  if (cleanupTableNavigation) {
    cleanupTableNavigation()
  }
})

// Watch for changes in assessment selection
watch(
  () => props.assessmentId,
  async (newAssessmentId) => {
    if (newAssessmentId) {
      selectAssessment(parseInt(newAssessmentId))
    }
  },
)
</script>

<template>
  <div
    data-grading-interface
    class="space-y-6"
    role="main"
    aria-label="Assessment Grading Interface"
    :aria-busy="loading"
  >
    <!-- Skip Links for Accessibility -->
    <div class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50">
      <a
        href="#grading-title"
        class="bg-blue-600 text-white px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Skip to main heading
      </a>
      <a
        href="#grading-mode-tabs"
        class="bg-blue-600 text-white px-4 py-2 rounded ml-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Skip to grading modes
      </a>
      <a
        href="#grading-table"
        class="bg-blue-600 text-white px-4 py-2 rounded ml-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Skip to grading table
      </a>
    </div>
    <!-- Header Section -->
    <div class="flex flex-col space-y-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 id="grading-title" class="text-2xl font-bold tracking-tight">Assessment Grading</h1>
          <p class="text-muted-foreground" id="grading-description">
            Grade student submissions and manage assessment scores
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-2" role="toolbar" aria-label="Grading actions">
          <!-- Auto-save Toggle -->
          <div class="flex items-center gap-2 px-3 py-1 border rounded-md">
            <Checkbox
              id="auto-save-toggle"
              :checked="isAutoSaveEnabled"
              @update:checked="toggleAutoSave"
              aria-label="Toggle auto-save"
            />
            <Label for="auto-save-toggle" class="text-sm">Auto-save</Label>
          </div>

          <Button
            variant="outline"
            size="sm"
            @click="keyboardShortcuts.toggleHelp()"
            aria-label="Toggle keyboard shortcuts help"
          >
            <Keyboard class="h-4 w-4 mr-2" />
            Shortcuts
          </Button>

          <Button
            variant="outline"
            size="sm"
            @click="handleDiscardChanges"
            :disabled="!hasUnsavedChanges"
            aria-label="Discard all unsaved changes"
          >
            <RotateCcw class="h-4 w-4 mr-2" />
            Discard
          </Button>

          <Button
            size="sm"
            @click="handleSaveAll"
            :disabled="!hasUnsavedChanges || loading || hasValidationErrorsComputed"
            aria-label="Save all changes"
          >
            <Save class="h-4 w-4 mr-2" />
            <LoadingSpinner v-if="loading || isSaving" size="sm" class="mr-1" />
            Save All
          </Button>
        </div>
      </div>

      <!-- Status Bar -->
      <Card role="status" aria-label="Grading progress and status">
        <CardContent class="py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-6">
              <!-- Grading Progress -->
              <div
                class="flex items-center gap-2"
                role="progressbar"
                :aria-valuenow="gradingProgress.percentage"
                aria-valuemin="0"
                aria-valuemax="100"
                :aria-label="`Grading progress: ${gradingProgress.completed} of ${gradingProgress.total} completed`"
              >
                <CheckCircle class="h-4 w-4 text-green-600" aria-hidden="true" />
                <span class="text-sm font-medium">
                  Progress: {{ gradingProgress.completed }}/{{ gradingProgress.total }} ({{
                    gradingProgress.percentage
                  }}%)
                </span>
              </div>

              <!-- Status Counts -->
              <div class="flex items-center gap-4" role="group" aria-label="Grade status counts">
                <Badge variant="default" class="gap-1" :aria-label="`${statusCounts[GradeStatus.FINAL]} final grades`">
                  <span class="w-2 h-2 bg-green-500 rounded-full" aria-hidden="true"></span>
                  Final: {{ statusCounts[GradeStatus.FINAL] }}
                </Badge>
                <Badge
                  variant="secondary"
                  class="gap-1"
                  :aria-label="`${statusCounts[GradeStatus.DRAFT]} draft grades`"
                >
                  <span class="w-2 h-2 bg-yellow-500 rounded-full" aria-hidden="true"></span>
                  Draft: {{ statusCounts[GradeStatus.DRAFT] }}
                </Badge>
                <Badge
                  variant="outline"
                  class="gap-1"
                  :aria-label="`${statusCounts[GradeStatus.MISSING]} missing grades`"
                >
                  <span class="w-2 h-2 bg-gray-500 rounded-full" aria-hidden="true"></span>
                  Missing: {{ statusCounts[GradeStatus.MISSING] }}
                </Badge>
              </div>
            </div>

            <!-- Auto-save and Validation Status -->
            <div class="flex items-center gap-4" role="group" aria-label="System status indicators">
              <!-- Connection Status -->
              <div
                class="flex items-center gap-2 text-sm"
                :aria-label="`Connection status: ${connectionStatus === 'connected' ? 'Connected' : connectionStatus === 'syncing' ? 'Syncing' : connectionStatus === 'error' ? 'Connection Error' : 'Resolving Conflicts'}`"
              >
                <component
                  :is="connectionStatus === 'connected' ? Wifi : connectionStatus === 'syncing' ? Clock : WifiOff"
                  class="h-4 w-4"
                  :class="{
                    'text-green-600': connectionStatus === 'connected',
                    'text-blue-600 animate-spin': connectionStatus === 'syncing',
                    'text-red-600': connectionStatus === 'error',
                    'text-yellow-600': connectionStatus === 'resolving-conflicts',
                  }"
                  aria-hidden="true"
                />
                <span class="text-muted-foreground">
                  {{
                    connectionStatus === 'connected'
                      ? 'Connected'
                      : connectionStatus === 'syncing'
                        ? 'Syncing...'
                        : connectionStatus === 'error'
                          ? 'Connection Error'
                          : 'Resolving Conflicts'
                  }}
                </span>
              </div>

              <!-- Validation Status -->
              <div
                v-if="totalValidationIssues > 0"
                class="flex items-center gap-2 text-sm"
                :aria-label="`Validation status: ${validationStatusMessage}`"
              >
                <component
                  :is="hasValidationErrorsComputed ? ShieldAlert : Shield"
                  class="h-4 w-4"
                  :class="hasValidationErrorsComputed ? 'text-red-600' : 'text-yellow-600'"
                  aria-hidden="true"
                />
                <span :class="hasValidationErrorsComputed ? 'text-red-600' : 'text-yellow-600'">
                  {{ validationStatusMessage }}
                </span>
              </div>

              <!-- Save Status -->
              <div
                class="flex items-center gap-2 text-sm text-muted-foreground"
                :aria-label="`Save status: ${saveStatusMessage}`"
              >
                <Clock class="h-4 w-4" aria-hidden="true" />
                <span>{{ saveStatusMessage }}</span>
                <div
                  v-if="hasUnsavedChanges"
                  class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"
                  aria-label="Unsaved changes indicator"
                  role="status"
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Error Alerts -->
    <Alert v-if="error" variant="destructive">
      <AlertTriangle class="h-4 w-4" />
      <AlertDescription>
        {{ error }}
      </AlertDescription>
    </Alert>

    <!-- Validation Errors Alert -->
    <Alert v-if="hasValidationErrorsComputed" variant="destructive">
      <ShieldAlert class="h-4 w-4" />
      <AlertDescription>
        <div class="space-y-2">
          <p class="font-medium">Validation errors prevent saving:</p>
          <ul class="list-disc list-inside space-y-1">
            <li v-for="[gradeId, errors] in validationErrors" :key="gradeId">
              <template v-if="errors.errors">
                <template v-for="error in errors.errors" :key="error.field">
                  Grade ID {{ gradeId }}: {{ error.message }}
                </template>
              </template>
            </li>
          </ul>
        </div>
      </AlertDescription>
    </Alert>

    <!-- Validation Warnings Alert -->
    <Alert v-if="hasValidationWarnings && !hasValidationErrorsComputed" variant="default">
      <Shield class="h-4 w-4" />
      <AlertDescription>
        <div class="space-y-2">
          <p class="font-medium">Validation warnings:</p>
          <ul class="list-disc list-inside space-y-1">
            <li v-for="[gradeId, errors] in validationErrors" :key="gradeId">
              <template v-if="errors.warnings">
                <template v-for="warning in errors.warnings" :key="warning.field">
                  Grade ID {{ gradeId }}: {{ warning.message }}
                </template>
              </template>
            </li>
          </ul>
        </div>
      </AlertDescription>
    </Alert>

    <!-- Conflict Resolution -->
    <Card v-if="conflicts.length > 0" class="border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle class="flex items-center gap-2 text-yellow-800">
          <AlertTriangle class="h-5 w-5" />
          Resolve Conflicts
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <p class="text-sm text-yellow-700">
            The following grades have conflicts that need to be resolved before saving:
          </p>

          <div v-for="conflict in conflicts" :key="conflict.gradeId" class="border rounded-lg p-4 bg-white">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-medium">Grade ID: {{ conflict.gradeId }}</h4>
              <Badge variant="outline">{{ conflict.conflictFields?.length || 0 }} conflicts</Badge>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label class="text-xs font-medium text-muted-foreground">Your Changes</Label>
                <div class="text-sm">
                  <!-- Show local version details -->
                  <pre class="text-xs bg-gray-100 p-2 rounded">{{
                    JSON.stringify(conflict.localVersion, null, 2)
                  }}</pre>
                </div>
              </div>

              <div>
                <Label class="text-xs font-medium text-muted-foreground">Server Version</Label>
                <div class="text-sm">
                  <!-- Show server version details -->
                  <pre class="text-xs bg-gray-100 p-2 rounded">{{
                    JSON.stringify(conflict.serverVersion, null, 2)
                  }}</pre>
                </div>
              </div>

              <div class="space-y-2">
                <Label class="text-xs font-medium text-muted-foreground">Resolution</Label>
                <div class="flex flex-col gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    @click="
                      () => {
                        /* Handle conflict resolution */
                      }
                    "
                  >
                    Use My Changes
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    @click="
                      () => {
                        /* Handle conflict resolution */
                      }
                    "
                  >
                    Use Server Version
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Keyboard Shortcuts Help -->
    <KeyboardShortcutsHelp
      :open="keyboardShortcuts.showHelp.value"
      :categories="keyboardShortcuts.shortcutsByCategory.value"
      :format-shortcut-key="keyboardShortcuts.formatShortcutKey"
      @update:open="keyboardShortcuts.showHelp.value = $event"
    />

    <!-- Main Grading Interface -->
    <ErrorBoundary
      ref="errorBoundaryRef"
      title="Grading Interface Error"
      :show-details="true"
      :context="{
        operation: 'grading_interface',
        resource: 'assessment_grading',
        courseId: parseInt(courseId),
        timestamp: new Date(),
      }"
      @retry="() => refreshData()"
    >
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Grading Interface</CardTitle>

            <!-- Assessment Selection Info -->
            <div v-if="selectedAssessmentId" class="text-sm text-muted-foreground">
              <FileText class="h-4 w-4 inline mr-1" />
              Assessment ID: {{ selectedAssessmentId }}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <!-- Grading Mode Tabs -->
          <Tabs
            id="grading-mode-tabs"
            :model-value="activeTab"
            @update:model-value="handleModeChange"
            class="w-full"
            role="tablist"
            aria-label="Grading mode selection"
          >
            <TabsList class="grid w-full grid-cols-2 mb-6" role="tablist">
              <TabsTrigger
                v-for="option in gradingModeOptions"
                :key="option.value"
                :value="option.value"
                class="gap-2"
                :aria-label="option.ariaLabel"
                :aria-selected="activeTab === option.value"
                role="tab"
                :tabindex="activeTab === option.value ? 0 : -1"
              >
                <component :is="option.icon" class="h-4 w-4" aria-hidden="true" />
                <span class="hidden sm:inline">{{ option.label }}</span>
                <span class="sm:hidden">{{ option.label.split(' ')[1] }}</span>
              </TabsTrigger>
            </TabsList>

            <!-- Mode Descriptions -->
            <div class="mb-6" role="region" aria-label="Current mode description">
              <Alert>
                <Info class="h-4 w-4" aria-hidden="true" />
                <AlertDescription>
                  {{ gradingModeOptions.find((opt) => opt.value === activeTab)?.description }}
                </AlertDescription>
              </Alert>
            </div>

            <!-- By Student Tab Content -->
            <TabsContent
              :value="GradingMode.BY_STUDENT"
              class="space-y-4"
              role="tabpanel"
              aria-label="Grade by student interface"
            >
              <GradingTable
                id="grading-table"
                :course-id="courseId"
                :mode="GradingMode.BY_STUDENT"
                :selected-assessment-id="selectedAssessmentId || undefined"
                :selected-student-id="selectedStudentId || undefined"
                :student-grading-data="studentGradingData"
                :component-grading-data="componentGradingData"
                @grade-change="handleGradeChange"
                @grade-change-real="handleUpdateGradeReal"
                @load-student-data="handleLoadStudentData"
                @load-component-data="handleLoadComponentData"
                @bulk-update-real="handleBulkUpdateGradesReal"
              />
            </TabsContent>

            <!-- By Component Tab Content -->
            <TabsContent
              :value="GradingMode.BY_COMPONENT"
              class="space-y-4"
              role="tabpanel"
              aria-label="Grade by component interface"
            >
              <GradingTable
                :course-id="courseId"
                :mode="GradingMode.BY_COMPONENT"
                :selected-assessment-id="selectedAssessmentId || undefined"
                :selected-student-id="selectedStudentId || undefined"
                :student-grading-data="studentGradingData"
                :component-grading-data="componentGradingData"
                @grade-change="handleGradeChange"
                @grade-change-real="handleUpdateGradeReal"
                @load-student-data="handleLoadStudentData"
                @load-component-data="handleLoadComponentData"
                @bulk-update-real="handleBulkUpdateGradesReal"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </ErrorBoundary>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8" role="status" aria-label="Loading grades">
      <LoadingSpinner size="lg" />
      <span class="ml-2 text-muted-foreground">Loading grades...</span>
    </div>

    <!-- Screen Reader Live Region for Announcements -->
    <div id="sr-announcements" aria-live="polite" aria-atomic="true" class="sr-only"></div>

    <!-- Screen Reader Live Region for Urgent Announcements -->
    <div id="sr-urgent-announcements" aria-live="assertive" aria-atomic="true" class="sr-only"></div>
  </div>
</template>

<style scoped>
/* Screen reader only class for announcements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators for better accessibility */
[data-grading-interface] button:focus,
[data-grading-interface] input:focus,
[data-grading-interface] [tabindex='0']:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .text-muted-foreground {
    color: inherit;
    opacity: 0.8;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse {
    animation: none;
  }

  * {
    transition: none !important;
  }
}
</style>
