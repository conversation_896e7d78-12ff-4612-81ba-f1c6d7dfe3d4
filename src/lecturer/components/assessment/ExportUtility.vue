<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Label } from '@/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Separator } from '@/shared/components/ui/separator'
import { Progress } from '@/shared/components/ui/progress'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import { useAssessmentStore } from '@/lecturer/stores/assessment'
import { storeToRefs } from 'pinia'
import type { ExcelExportFilters, PdfExportFilters } from '@/lecturer/types/models/assessment'
import {
  Download,
  FileSpreadsheet,
  FileText,
  Settings,
  CheckCircle,
  Alert<PERSON>riangle,
  Info,
} from 'lucide-vue-next'

interface Props {
  courseOfferingId: number
  availableStudents?: { id: number; name: string }[]
  availableComponents?: { id: number; name: string }[]
}

const props = withDefaults(defineProps<Props>(), {
  availableStudents: () => [],
  availableComponents: () => [],
})

// Store
const assessmentStore = useAssessmentStore()
const { exportLoading, error } = storeToRefs(assessmentStore)

// Local state
const exportFormat = ref<'excel' | 'pdf'>('excel')
const showAdvancedOptions = ref(false)
const exportProgress = ref(0)
const exportStatus = ref<'idle' | 'preparing' | 'downloading' | 'complete' | 'error'>('idle')
const lastExportResult = ref<{ filename: string; size: number; timestamp: Date } | null>(null)

// Excel export filters
const excelFilters = ref<ExcelExportFilters>({
  include_excluded: false,
  score_status: '',
  student_ids: [],
  component_ids: [],
  include_statistics: true,
  include_grade_matrix: true,
})

// PDF export filters
const pdfFilters = ref<PdfExportFilters>({
  include_charts: true,
  include_statistics: true,
  include_grade_matrix: true,
  page_orientation: 'landscape',
  include_student_details: true,
})

// Computed properties
const isExporting = computed(() => exportLoading.value || exportStatus.value === 'preparing')
const canExport = computed(() => !isExporting.value && exportStatus.value !== 'downloading')

const currentFilters = computed(() => {
  return exportFormat.value === 'excel' ? excelFilters.value : pdfFilters.value
})

const exportButtonText = computed(() => {
  if (exportStatus.value === 'preparing') return 'Preparing Export...'
  if (exportStatus.value === 'downloading') return 'Downloading...'
  if (exportLoading.value) return 'Exporting...'
  return `Export ${exportFormat.value.toUpperCase()}`
})

const exportIcon = computed(() => {
  return exportFormat.value === 'excel' ? FileSpreadsheet : FileText
})

// Methods
const handleExport = async () => {
  exportStatus.value = 'preparing'
  exportProgress.value = 0

  try {
    // Simulate preparation progress
    const progressInterval = setInterval(() => {
      if (exportProgress.value < 30) {
        exportProgress.value += 5
      }
    }, 100)

    if (exportFormat.value === 'excel') {
      await assessmentStore.exportToExcel(props.courseOfferingId, excelFilters.value)
    } else {
      await assessmentStore.exportToPdf(props.courseOfferingId, pdfFilters.value)
    }

    clearInterval(progressInterval)
    exportProgress.value = 100
    exportStatus.value = 'complete'

    // Record successful export
    lastExportResult.value = {
      filename: `assessment-report-${props.courseOfferingId}-${new Date().toISOString().split('T')[0]}.${exportFormat.value === 'excel' ? 'xlsx' : 'pdf'}`,
      size: 0, // Size would be available from the actual response
      timestamp: new Date(),
    }

    // Reset status after a delay
    setTimeout(() => {
      exportStatus.value = 'idle'
      exportProgress.value = 0
    }, 3000)

  } catch (error) {
    exportStatus.value = 'error'
    console.error('Export failed:', error)
    
    setTimeout(() => {
      exportStatus.value = 'idle'
      exportProgress.value = 0
    }, 5000)
  }
}

const resetFilters = () => {
  if (exportFormat.value === 'excel') {
    excelFilters.value = {
      include_excluded: false,
      score_status: '',
      student_ids: [],
      component_ids: [],
      include_statistics: true,
      include_grade_matrix: true,
    }
  } else {
    pdfFilters.value = {
      include_charts: true,
      include_statistics: true,
      include_grade_matrix: true,
      page_orientation: 'landscape',
      include_student_details: true,
    }
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Download class="h-5 w-5" />
        Export Assessment Data
      </CardTitle>
    </CardHeader>
    
    <CardContent class="space-y-6">
      <!-- Export Format Selection -->
      <div class="space-y-3">
        <Label class="text-base font-medium">Export Format</Label>
        <div class="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            :class="{ 'border-primary bg-primary/5': exportFormat === 'excel' }"
            @click="exportFormat = 'excel'"
            class="h-auto p-4 flex flex-col items-center gap-2"
          >
            <FileSpreadsheet class="h-8 w-8" />
            <div class="text-center">
              <div class="font-medium">Excel</div>
              <div class="text-xs text-muted-foreground">Spreadsheet format</div>
            </div>
          </Button>
          
          <Button
            variant="outline"
            :class="{ 'border-primary bg-primary/5': exportFormat === 'pdf' }"
            @click="exportFormat = 'pdf'"
            class="h-auto p-4 flex flex-col items-center gap-2"
          >
            <FileText class="h-8 w-8" />
            <div class="text-center">
              <div class="font-medium">PDF</div>
              <div class="text-xs text-muted-foreground">Document format</div>
            </div>
          </Button>
        </div>
      </div>

      <!-- Advanced Options Toggle -->
      <div class="flex items-center justify-between">
        <Button
          variant="ghost"
          size="sm"
          @click="showAdvancedOptions = !showAdvancedOptions"
          class="gap-2"
        >
          <Settings class="h-4 w-4" />
          Advanced Options
        </Button>
        
        <Button variant="outline" size="sm" @click="resetFilters">
          Reset Filters
        </Button>
      </div>

      <!-- Advanced Options Panel -->
      <Card v-if="showAdvancedOptions" class="p-4">
        <div class="space-y-4">
          <!-- Excel-specific options -->
          <template v-if="exportFormat === 'excel'">
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label>Score Status Filter</Label>
                <Select v-model="excelFilters.score_status">
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="provisional">Provisional</SelectItem>
                    <SelectItem value="final">Final</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div class="space-y-3">
                <Label>Include Options</Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="excel-excluded"
                      v-model:checked="excelFilters.include_excluded"
                    />
                    <Label for="excel-excluded" class="text-sm">Include excluded scores</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="excel-statistics"
                      v-model:checked="excelFilters.include_statistics"
                    />
                    <Label for="excel-statistics" class="text-sm">Include statistics</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="excel-matrix"
                      v-model:checked="excelFilters.include_grade_matrix"
                    />
                    <Label for="excel-matrix" class="text-sm">Include grade matrix</Label>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- PDF-specific options -->
          <template v-else>
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label>Page Orientation</Label>
                <Select v-model="pdfFilters.page_orientation">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="portrait">Portrait</SelectItem>
                    <SelectItem value="landscape">Landscape</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div class="space-y-3">
                <Label>Include Options</Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="pdf-charts"
                      v-model:checked="pdfFilters.include_charts"
                    />
                    <Label for="pdf-charts" class="text-sm">Include charts</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="pdf-statistics"
                      v-model:checked="pdfFilters.include_statistics"
                    />
                    <Label for="pdf-statistics" class="text-sm">Include statistics</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="pdf-matrix"
                      v-model:checked="pdfFilters.include_grade_matrix"
                    />
                    <Label for="pdf-matrix" class="text-sm">Include grade matrix</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="pdf-details"
                      v-model:checked="pdfFilters.include_student_details"
                    />
                    <Label for="pdf-details" class="text-sm">Include student details</Label>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </Card>

      <!-- Export Progress -->
      <div v-if="exportProgress > 0 && exportStatus !== 'idle'" class="space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span>Export Progress</span>
          <span>{{ exportProgress }}%</span>
        </div>
        <Progress :value="exportProgress" class="h-2" />
      </div>

      <!-- Status Messages -->
      <Alert v-if="exportStatus === 'complete'" class="border-green-200 bg-green-50">
        <CheckCircle class="h-4 w-4 text-green-600" />
        <AlertDescription class="text-green-800">
          Export completed successfully!
          <span v-if="lastExportResult" class="block text-sm mt-1">
            {{ lastExportResult.filename }} • {{ formatFileSize(lastExportResult.size) }}
          </span>
        </AlertDescription>
      </Alert>

      <Alert v-if="exportStatus === 'error' || error" variant="destructive">
        <AlertTriangle class="h-4 w-4" />
        <AlertDescription>
          {{ error || 'Export failed. Please try again.' }}
        </AlertDescription>
      </Alert>

      <!-- Export Button -->
      <Button
        @click="handleExport"
        :disabled="!canExport"
        class="w-full gap-2"
        size="lg"
      >
        <LoadingSpinner v-if="isExporting" size="sm" />
        <component v-else :is="exportIcon" class="h-5 w-5" />
        {{ exportButtonText }}
      </Button>

      <!-- Info -->
      <Alert>
        <Info class="h-4 w-4" />
        <AlertDescription class="text-sm">
          The export will include all assessment data based on your selected filters.
          Large datasets may take a few moments to process.
        </AlertDescription>
      </Alert>
    </CardContent>
  </Card>
</template>
