<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Badge } from '@/shared/components/ui/badge'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import type { GradeMatrix, GradeMatrixFilters } from '@/lecturer/types/models/assessment'
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  Users,
  FileText,
  AlertTriangle,
  Eye,
  EyeOff,
} from 'lucide-vue-next'

interface Props {
  data: GradeMatrix | null
  loading?: boolean
  error?: string | null
  filters?: GradeMatrixFilters
}

interface Emits {
  (e: 'updateFilters', filters: GradeMatrixFilters): void
  (e: 'refresh'): void
  (e: 'export', format: 'excel' | 'pdf'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
  filters: () => ({
    include_excluded: false,
    score_status: '',
    student_ids: [],
    component_ids: [],
  }),
})

const emit = defineEmits<Emits>()

// Local filter state
const searchQuery = ref('')
const showFilters = ref(false)
const localFilters = ref<GradeMatrixFilters>({ ...props.filters })

// Computed properties
const filteredStudents = computed(() => {
  if (!props.data?.grade_matrix.students) return []
  
  let students = props.data.grade_matrix.students
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    students = students.filter(student => 
      student.name.toLowerCase().includes(query)
    )
  }
  
  return students
})

const components = computed(() => {
  return props.data?.grade_matrix.components || []
})

const hasData = computed(() => {
  return props.data && filteredStudents.value.length > 0
})

// Methods
const updateFilters = () => {
  emit('updateFilters', { ...localFilters.value })
}

const clearFilters = () => {
  localFilters.value = {
    include_excluded: false,
    score_status: '',
    student_ids: [],
    component_ids: [],
  }
  searchQuery.value = ''
  updateFilters()
}

const handleRefresh = () => {
  emit('refresh')
}

const handleExport = (format: 'excel' | 'pdf') => {
  emit('export', format)
}

const getScoreColor = (score: number | null) => {
  if (score === null || score === undefined) return 'text-gray-400'
  if (score >= 80) return 'text-green-600'
  if (score >= 70) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBadgeVariant = (score: number | null) => {
  if (score === null || score === undefined) return 'secondary'
  if (score >= 80) return 'default'
  if (score >= 70) return 'secondary'
  if (score >= 60) return 'outline'
  return 'destructive'
}

// Watch for filter changes
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters }
}, { deep: true })
</script>

<template>
  <div class="space-y-4">
    <!-- Header with Controls -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">Grade Matrix</h3>
        <p class="text-sm text-muted-foreground">
          Comprehensive view of all student grades across assessments
        </p>
      </div>
      
      <div class="flex items-center gap-2">
        <Button variant="outline" size="sm" @click="handleRefresh" :disabled="loading">
          <RefreshCw class="h-4 w-4 mr-1" :class="{ 'animate-spin': loading }" />
          Refresh
        </Button>
        
        <Button variant="outline" size="sm" @click="showFilters = !showFilters">
          <Filter class="h-4 w-4 mr-1" />
          Filters
        </Button>
        
        <Button variant="outline" size="sm" @click="handleExport('excel')" :disabled="!hasData">
          <Download class="h-4 w-4 mr-1" />
          Export
        </Button>
      </div>
    </div>

    <!-- Filters Panel -->
    <Card v-if="showFilters" class="p-4">
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div>
          <Label for="search">Search Students</Label>
          <div class="relative">
            <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              v-model="searchQuery"
              placeholder="Search by name..."
              class="pl-8"
            />
          </div>
        </div>
        
        <div>
          <Label for="score-status">Score Status</Label>
          <Select v-model="localFilters.score_status" @update:model-value="updateFilters">
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="provisional">Provisional</SelectItem>
              <SelectItem value="final">Final</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div class="flex items-center space-x-2">
          <Checkbox
            id="include-excluded"
            v-model:checked="localFilters.include_excluded"
            @update:checked="updateFilters"
          />
          <Label for="include-excluded" class="text-sm">Include excluded scores</Label>
        </div>
        
        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="clearFilters">
            Clear Filters
          </Button>
        </div>
      </div>
    </Card>

    <!-- Loading State -->
    <Card v-if="loading">
      <CardContent class="flex items-center justify-center py-12">
        <div class="text-center">
          <LoadingSpinner size="lg" />
          <p class="mt-2 text-muted-foreground">Loading grade matrix...</p>
        </div>
      </CardContent>
    </Card>

    <!-- Error State -->
    <Card v-else-if="error">
      <CardContent class="flex items-center justify-center py-12">
        <div class="text-center">
          <AlertTriangle class="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p class="text-red-600 font-medium">Failed to load grade matrix</p>
          <p class="text-sm text-muted-foreground">{{ error }}</p>
          <Button variant="outline" class="mt-4" @click="handleRefresh">
            Try Again
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Grade Matrix Table -->
    <Card v-else-if="hasData">
      <div class="overflow-auto max-h-[600px]">
        <table class="w-full">
          <thead class="sticky top-0 bg-white border-b">
            <tr>
              <th class="text-left p-3 font-medium min-w-[200px]">Student</th>
              <th
                v-for="(component, index) in components"
                :key="index"
                class="text-center p-3 font-medium min-w-[100px]"
              >
                {{ component }}
              </th>
              <th class="text-center p-3 font-medium min-w-[120px]">Weighted Total</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="student in filteredStudents"
              :key="student.id"
              class="border-b hover:bg-gray-50"
            >
              <td class="p-3">
                <div>
                  <div class="font-medium">{{ student.name }}</div>
                  <div class="text-sm text-muted-foreground">ID: {{ student.id }}</div>
                </div>
              </td>
              <td
                v-for="(score, index) in student.component_scores"
                :key="index"
                class="p-3 text-center"
              >
                <Badge
                  v-if="score !== null && score !== undefined"
                  :variant="getScoreBadgeVariant(score)"
                  :class="getScoreColor(score)"
                >
                  {{ score.toFixed(1) }}%
                </Badge>
                <span v-else class="text-gray-400">-</span>
              </td>
              <td class="p-3 text-center">
                <Badge
                  :variant="getScoreBadgeVariant(student.weighted_total)"
                  :class="getScoreColor(student.weighted_total)"
                  class="font-semibold"
                >
                  {{ student.weighted_total.toFixed(1) }}%
                </Badge>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Card>

    <!-- No Data State -->
    <Card v-else>
      <CardContent class="flex items-center justify-center py-12">
        <div class="text-center">
          <FileText class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p class="text-muted-foreground">No grade data available</p>
          <p class="text-sm text-muted-foreground">
            Try adjusting your filters or refresh the data
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
