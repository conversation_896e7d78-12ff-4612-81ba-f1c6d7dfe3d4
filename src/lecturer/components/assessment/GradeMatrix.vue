<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { <PERSON><PERSON> } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Badge } from '@/shared/components/ui/badge'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shared/components/ui/tooltip'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import GradeDisplay from './GradeDisplay.vue'
import { useAssessmentApi } from '@/lecturer/composables/useAssessmentApi'
import { useGradeScale } from '@/lecturer/composables/useGradeScale'
import { GradeStatus } from '@/lecturer/types/models/assessment'
import type { GradeEntry, Assessment, AssessmentDetail } from '@/lecturer/types/models/assessment'
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Search,
  Filter,
  Calculator,
  Eye,
  EyeOff,
  Info,
  AlertTriangle,
  CheckCircle,
  Clock,
  Minus,
} from 'lucide-vue-next'

interface Props {
  courseId: string
}

interface StudentGradeMatrix {
  student_id: number
  student_name: string
  student_email: string
  grades: Map<number, GradeEntry> // assessment_detail_id -> GradeEntry
  weighted_total: number
  letter_grade: string
  overall_percentage: number
}

interface AssessmentColumn {
  assessment: Assessment
  details: AssessmentDetail[]
  total_weight: number
}

const props = defineProps<Props>()
console.log('props', props)
// State
const loading = ref(false)
const error = ref<string | null>(null)
const assessments = ref<Assessment[]>([])
const gradeMatrix = ref<StudentGradeMatrix[]>([])
const assessmentColumns = ref<AssessmentColumn[]>([])

// Filters and sorting
const searchQuery = ref('')
const sortBy = ref<string>('student_name')
const sortOrder = ref<'asc' | 'desc'>('asc')
const showFilters = ref(false)
const selectedStatuses = ref<GradeStatus[]>([])
const showExcluded = ref(false)
const scoreRangeMin = ref<number | null>(null)
const scoreRangeMax = ref<number | null>(null)
const filterByAssessment = ref<number | null>(null)
const showOnlyMissing = ref(false)
const showOnlyLate = ref(false)

// Virtual scrolling
const parentRef = ref<HTMLElement>()
const scrollElementRef = ref<HTMLElement>()

// Responsive
const isMobile = ref(false)
const visibleColumns = ref<Set<string>>(new Set())

// API and composables
const api = useAssessmentApi()
const { getGradeLetter, getGradeColor } = useGradeScale()

// Computed properties
const filteredMatrix = computed(() => {
  let filtered = gradeMatrix.value

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (student) =>
        student.student_name.toLowerCase().includes(query) || student.student_email.toLowerCase().includes(query),
    )
  }

  // Status filter
  if (selectedStatuses.value.length > 0) {
    filtered = filtered.filter((student) => {
      return Array.from(student.grades.values()).some((grade) => selectedStatuses.value.includes(grade.status))
    })
  }

  // Exclusion filter
  if (!showExcluded.value) {
    filtered = filtered.filter((student) => {
      return Array.from(student.grades.values()).some((grade) => !grade.is_excluded)
    })
  }

  // Assessment-specific filter
  if (filterByAssessment.value) {
    filtered = filtered.filter((student) => {
      return Array.from(student.grades.values()).some((grade) => {
        // Find the assessment detail that belongs to the selected assessment
        const assessmentDetail = assessmentColumns.value
          .find((col) => col.assessment.id === filterByAssessment.value)
          ?.details.find((detail) => detail.id === grade.assessment_detail_id)
        return assessmentDetail !== undefined
      })
    })
  }

  // Missing grades filter
  if (showOnlyMissing.value) {
    filtered = filtered.filter((student) => {
      return Array.from(student.grades.values()).some((grade) => grade.status === GradeStatus.MISSING)
    })
  }

  // Late submissions filter
  if (showOnlyLate.value) {
    filtered = filtered.filter((student) => {
      return Array.from(student.grades.values()).some((grade) => grade.is_late)
    })
  }

  // Score range filter
  if (scoreRangeMin.value !== null || scoreRangeMax.value !== null) {
    filtered = filtered.filter((student) => {
      const percentage = student.overall_percentage
      const min = scoreRangeMin.value ?? 0
      const max = scoreRangeMax.value ?? 100
      return percentage >= min && percentage <= max
    })
  }

  return filtered
})

const sortedMatrix = computed(() => {
  const sorted = [...filteredMatrix.value]

  sorted.sort((a, b) => {
    let aValue: any
    let bValue: any

    // Check if sorting by assessment column
    if (sortBy.value.startsWith('assessment_')) {
      const assessmentDetailId = parseInt(sortBy.value.replace('assessment_', ''))
      const aGrade = a.grades.get(assessmentDetailId)
      const bGrade = b.grades.get(assessmentDetailId)

      // Handle missing grades - put them at the end
      if (!aGrade && !bGrade) return 0
      if (!aGrade) return sortOrder.value === 'asc' ? 1 : -1
      if (!bGrade) return sortOrder.value === 'asc' ? -1 : 1

      aValue = aGrade.percentage_score
      bValue = bGrade.percentage_score
    } else {
      switch (sortBy.value) {
        case 'student_name':
          aValue = a.student_name
          bValue = b.student_name
          break
        case 'overall_percentage':
          aValue = a.overall_percentage
          bValue = b.overall_percentage
          break
        case 'weighted_total':
          aValue = a.weighted_total
          bValue = b.weighted_total
          break
        case 'letter_grade':
          // Sort letter grades by their numeric equivalent
          const gradeOrder = { A: 5, B: 4, C: 3, D: 2, F: 1 }
          aValue = gradeOrder[a.letter_grade as keyof typeof gradeOrder] || 0
          bValue = gradeOrder[b.letter_grade as keyof typeof gradeOrder] || 0
          break
        default:
          aValue = a.student_name
          bValue = b.student_name
      }
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue)
      return sortOrder.value === 'asc' ? comparison : -comparison
    } else {
      const comparison = aValue - bValue
      return sortOrder.value === 'asc' ? comparison : -comparison
    }
  })

  return sorted
})

// Column definitions for the matrix
const columnDefinitions = computed(() => {
  const columns = [
    {
      key: 'student_name',
      label: 'Student',
      width: 200,
      sortable: true,
      sticky: true,
      mobile: true,
    },
  ]

  // Add assessment columns
  assessmentColumns.value.forEach((assessmentCol) => {
    assessmentCol.details.forEach((detail) => {
      columns.push({
        key: `assessment_${detail.id}`,
        label: `${assessmentCol.assessment.name} - ${detail.name}`,
        width: 120,
        sortable: true,
        sticky: false,
        mobile: false,
        assessment: assessmentCol.assessment,
        detail: detail,
      })
    })
  })

  // Add summary columns
  columns.push(
    {
      key: 'weighted_total',
      label: 'Weighted Total',
      width: 120,
      sortable: true,
      sticky: false,
      mobile: true,
    },
    {
      key: 'overall_percentage',
      label: 'Overall %',
      width: 100,
      sortable: true,
      sticky: false,
      mobile: true,
    },
    {
      key: 'letter_grade',
      label: 'Letter Grade',
      width: 100,
      sortable: true,
      sticky: false,
      mobile: true,
    },
  )

  return columns
})

const displayColumns = computed(() => {
  if (isMobile.value) {
    return columnDefinitions.value.filter((col) => col.mobile)
  }
  return columnDefinitions.value.filter((col) => visibleColumns.value.has(col.key))
})

const totalWidth = computed(() => {
  return displayColumns.value.reduce((sum, col) => sum + col.width, 0)
})

// Virtual scrolling setup
const virtualizer = computed(() => {
  if (!parentRef.value) return null

  return useVirtualizer({
    count: sortedMatrix.value.length,
    getScrollElement: () => scrollElementRef.value,
    estimateSize: () => 60, // Row height
    overscan: 10,
  })
})

// Methods
const fetchData = async () => {
  loading.value = true
  error.value = null

  try {
    // Fetch assessments
    const assessmentsResponse = await api.assessments.getAll(parseInt(props.courseId))
    if (!assessmentsResponse.success) {
      throw new Error(assessmentsResponse.message || 'Failed to fetch assessments')
    }
    assessments.value = Array.isArray(assessmentsResponse.data) ? assessmentsResponse.data : []

    // Build assessment columns
    assessmentColumns.value = assessments.value.map((assessment) => ({
      assessment,
      details: assessment.details || [],
      total_weight: assessment.weight,
    }))

    // Fetch all grades for the course
    const gradesResponse = await api.grades.getAll({
      course_offering_id: parseInt(props.courseId),
      per_page: 1000, // Get all grades
    })

    if (!gradesResponse.success) {
      throw new Error(gradesResponse.message || 'Failed to fetch grades')
    }

    const grades = Array.isArray(gradesResponse.data) ? gradesResponse.data : []
    buildGradeMatrix(grades)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load data'
    console.error('Grade matrix fetch error:', err)
  } finally {
    loading.value = false
  }
}

const buildGradeMatrix = (grades: GradeEntry[]) => {
  const studentMap = new Map<number, StudentGradeMatrix>()

  // Group grades by student
  grades.forEach((grade) => {
    if (!studentMap.has(grade.student_id)) {
      studentMap.set(grade.student_id, {
        student_id: grade.student_id,
        student_name: grade.student_name,
        student_email: grade.student_email,
        grades: new Map(),
        weighted_total: 0,
        letter_grade: '',
        overall_percentage: 0,
      })
    }

    const student = studentMap.get(grade.student_id)!
    student.grades.set(grade.assessment_detail_id, grade)
  })

  // Calculate weighted totals and letter grades
  studentMap.forEach((student) => {
    calculateStudentTotals(student)
  })

  gradeMatrix.value = Array.from(studentMap.values())
}

const calculateStudentTotals = (student: StudentGradeMatrix) => {
  let totalWeightedPoints = 0
  let totalPossibleWeighted = 0

  assessmentColumns.value.forEach((assessmentCol) => {
    assessmentCol.details.forEach((detail) => {
      const grade = student.grades.get(detail.id)
      if (grade && !grade.is_excluded) {
        const weightedPoints = (grade.percentage_score / 100) * detail.weight
        totalWeightedPoints += weightedPoints
        totalPossibleWeighted += detail.weight
      }
    })
  })

  student.weighted_total = totalWeightedPoints
  student.overall_percentage = totalPossibleWeighted > 0 ? (totalWeightedPoints / totalPossibleWeighted) * 100 : 0
  student.letter_grade = calculateLetterGrade(student.overall_percentage)
}

const calculateLetterGrade = (percentage: number): string => {
  return getGradeLetter(percentage)
}

const getLetterGradeColor = (percentage: number): string => {
  const colorClasses = getGradeColor(percentage)
  // Extract just the text color class for backward compatibility
  const textColorMatch = colorClasses.match(/text-[\w-]+/)
  return textColorMatch ? textColorMatch[0] : 'text-gray-600'
}

const getGradeStatusIcon = (status: GradeStatus) => {
  switch (status) {
    case GradeStatus.FINAL:
      return CheckCircle
    case GradeStatus.PROVISIONAL:
      return Clock
    case GradeStatus.DRAFT:
      return AlertTriangle
    case GradeStatus.PENDING:
      return Clock
    case GradeStatus.MISSING:
      return Minus
    default:
      return Minus
  }
}

const getGradeStatusColor = (status: GradeStatus): string => {
  switch (status) {
    case GradeStatus.FINAL:
      return 'text-green-600'
    case GradeStatus.PROVISIONAL:
      return 'text-blue-600'
    case GradeStatus.DRAFT:
      return 'text-yellow-600'
    case GradeStatus.PENDING:
      return 'text-orange-600'
    case GradeStatus.MISSING:
      return 'text-gray-400'
    default:
      return 'text-gray-400'
  }
}

const handleSort = (column: string) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column
    sortOrder.value = 'asc'
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedStatuses.value = []
  showExcluded.value = false
  scoreRangeMin.value = null
  scoreRangeMax.value = null
  filterByAssessment.value = null
  showOnlyMissing.value = false
  showOnlyLate.value = false
}

const checkResponsive = () => {
  isMobile.value = window.innerWidth < 768

  if (isMobile.value) {
    // Show only essential columns on mobile
    visibleColumns.value = new Set(['student_name', 'overall_percentage', 'letter_grade'])
  } else {
    // Show all columns on desktop
    visibleColumns.value = new Set(columnDefinitions.value.map((col) => col.key))
  }
}

// Lifecycle
onMounted(() => {
  checkResponsive()
  window.addEventListener('resize', checkResponsive)
  fetchData()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkResponsive)
})

// Watch for course changes
watch(() => props.courseId, fetchData)
</script>

<template>
  <div class="space-y-4">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">Comprehensive Grade Matrix</h3>
        <p class="text-sm text-muted-foreground">View all student grades across all assessments</p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          @click="showFilters = !showFilters"
          :aria-expanded="showFilters"
          aria-controls="grade-matrix-filters"
        >
          <Filter class="h-4 w-4 mr-2" />
          Filters
        </Button>
        <Button variant="outline" size="sm" @click="fetchData" :disabled="loading">
          <ArrowUpDown class="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
    </div>

    <!-- Filters Panel -->
    <Card v-if="showFilters" id="grade-matrix-filters" class="p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Search Students</label>
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input v-model="searchQuery" placeholder="Name or email..." class="pl-10" />
          </div>
        </div>

        <!-- Assessment Filter -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Filter by Assessment</label>
          <Select v-model="filterByAssessment">
            <SelectTrigger>
              <SelectValue placeholder="All assessments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem :value="null">All assessments</SelectItem>
              <SelectItem v-for="assessment in assessments" :key="assessment.id" :value="assessment.id">
                {{ assessment.name }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Status Filter -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Grade Status</label>
          <Select multiple v-model="selectedStatuses">
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem :value="GradeStatus.FINAL">Final</SelectItem>
              <SelectItem :value="GradeStatus.PROVISIONAL">Provisional</SelectItem>
              <SelectItem :value="GradeStatus.DRAFT">Draft</SelectItem>
              <SelectItem :value="GradeStatus.PENDING">Pending</SelectItem>
              <SelectItem :value="GradeStatus.MISSING">Missing</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Score Range -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Score Range (%)</label>
          <div class="flex gap-2">
            <Input v-model.number="scoreRangeMin" type="number" placeholder="Min" min="0" max="100" />
            <Input v-model.number="scoreRangeMax" type="number" placeholder="Max" min="0" max="100" />
          </div>
        </div>

        <!-- Quick Filters -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Quick Filters</label>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <Checkbox id="show-excluded" v-model:checked="showExcluded" />
              <label for="show-excluded" class="text-sm">Show excluded grades</label>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox id="show-only-missing" v-model:checked="showOnlyMissing" />
              <label for="show-only-missing" class="text-sm">Show only missing grades</label>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox id="show-only-late" v-model:checked="showOnlyLate" />
              <label for="show-only-late" class="text-sm">Show only late submissions</label>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="space-y-2">
          <label class="text-sm font-medium">Actions</label>
          <div class="space-y-2">
            <Button variant="outline" size="sm" @click="clearFilters" class="w-full">Clear All Filters</Button>
            <Badge variant="secondary" class="text-xs">
              {{ filteredMatrix.length }} of {{ gradeMatrix.length }} students
            </Badge>
          </div>
        </div>
      </div>
    </Card>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <LoadingSpinner size="lg" />
      <span class="ml-2 text-muted-foreground">Loading grade matrix...</span>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="p-6 text-center">
      <AlertTriangle class="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 class="text-lg font-semibold mb-2">Failed to Load Grade Matrix</h3>
      <p class="text-muted-foreground mb-4">{{ error }}</p>
      <Button @click="fetchData">Try Again</Button>
    </Card>

    <!-- Grade Matrix Table -->
    <Card v-else-if="!loading && sortedMatrix.length > 0">
      <div
        ref="parentRef"
        class="relative overflow-auto"
        style="height: 600px"
        role="table"
        aria-label="Comprehensive grade matrix"
      >
        <!-- Table Header -->
        <div class="sticky top-0 z-10 bg-white border-b" role="rowgroup">
          <div class="flex items-center h-12 px-4" :style="{ width: `${totalWidth}px` }" role="row">
            <div
              v-for="column in displayColumns"
              :key="column.key"
              :style="{ width: `${column.width}px` }"
              class="flex items-center justify-between px-2 text-sm font-medium text-muted-foreground"
              role="columnheader"
              :class="{ 'cursor-pointer hover:text-foreground': column.sortable }"
              @click="column.sortable ? handleSort(column.key) : null"
            >
              <span class="truncate">{{ column.label }}</span>
              <div v-if="column.sortable" class="flex items-center ml-1">
                <ArrowUp v-if="sortBy === column.key && sortOrder === 'asc'" class="h-3 w-3" />
                <ArrowDown v-else-if="sortBy === column.key && sortOrder === 'desc'" class="h-3 w-3" />
                <ArrowUpDown v-else class="h-3 w-3 opacity-50" />
              </div>
            </div>
          </div>
        </div>

        <!-- Virtual Scrolling Container -->
        <div ref="scrollElementRef" class="overflow-auto" style="height: calc(100% - 48px)">
          <div
            v-if="virtualizer"
            :style="{ height: `${virtualizer.getTotalSize()}px`, position: 'relative' }"
            role="rowgroup"
          >
            <!-- Virtual Rows -->
            <div
              v-for="virtualRow in virtualizer.getVirtualItems()"
              :key="virtualRow.key"
              :style="{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }"
            >
              <div
                v-if="sortedMatrix[virtualRow.index]"
                class="flex items-center h-full px-4 border-b hover:bg-gray-50"
                :style="{ width: `${totalWidth}px` }"
                role="row"
              >
                <!-- Student Name Column -->
                <div :style="{ width: `${displayColumns[0].width}px` }" class="px-2 font-medium truncate" role="cell">
                  {{ sortedMatrix[virtualRow.index].student_name }}
                </div>

                <!-- Assessment Grade Columns -->
                <div
                  v-for="column in displayColumns.slice(1, -3)"
                  :key="column.key"
                  :style="{ width: `${column.width}px` }"
                  class="px-2 text-center"
                  role="cell"
                >
                  <GradeDisplay
                    v-if="column.detail"
                    :percentage="sortedMatrix[virtualRow.index].grades.get(column.detail.id)?.percentage_score"
                    :status="sortedMatrix[virtualRow.index].grades.get(column.detail.id)?.status || GradeStatus.MISSING"
                    :is-excluded="sortedMatrix[virtualRow.index].grades.get(column.detail.id)?.is_excluded || false"
                    :is-late="sortedMatrix[virtualRow.index].grades.get(column.detail.id)?.is_late || false"
                    :show-percentage="true"
                    :show-status="true"
                    size="sm"
                    variant="outline"
                  />
                </div>

                <!-- Summary Columns -->
                <div
                  :style="{ width: `${displayColumns[displayColumns.length - 3].width}px` }"
                  class="px-2 text-center font-medium"
                  role="cell"
                >
                  {{ sortedMatrix[virtualRow.index].weighted_total.toFixed(1) }}
                </div>
                <div
                  :style="{ width: `${displayColumns[displayColumns.length - 2].width}px` }"
                  class="px-2 text-center font-medium"
                  role="cell"
                >
                  {{ sortedMatrix[virtualRow.index].overall_percentage.toFixed(1) }}%
                </div>
                <div
                  :style="{ width: `${displayColumns[displayColumns.length - 1].width}px` }"
                  class="px-2 text-center"
                  role="cell"
                >
                  <GradeDisplay
                    :percentage="sortedMatrix[virtualRow.index].overall_percentage"
                    :show-percentage="false"
                    :show-status="false"
                    size="md"
                    variant="default"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- Empty State -->
    <Card v-else-if="!loading" class="p-6 text-center">
      <Info class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
      <h3 class="text-lg font-semibold mb-2">No Grade Data Available</h3>
      <p class="text-muted-foreground">No grades have been recorded for this course yet.</p>
    </Card>
  </div>
</template>

<style scoped>
/* Ensure proper scrolling behavior */
.overflow-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
