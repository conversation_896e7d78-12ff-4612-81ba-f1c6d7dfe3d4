<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Textarea } from '@/shared/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Badge } from '@/shared/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import { Progress } from '@/shared/components/ui/progress'
import { Alert, AlertDescription } from '@/shared/components/ui/alert'
import { Separator } from '@/shared/components/ui/separator'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import { useAssessmentStore } from '@/lecturer/stores/assessment'
import { GradeStatus, AcademicIntegrityStatus } from '@/lecturer/types/models/assessment'
import type {
  GradeEntry,
  BulkGradeUpdate,
  BulkOperationProgress,
  BulkGradeUpdateRequest,
  BulkScoreUpdate,
  BulkUpdateResponse,
  BulkPartialFailureResponse,
  ScoreStatus,
} from '@/lecturer/types/models/assessment'
import {
  Users,
  CheckCircle,
  AlertTriangle,
  X,
  Play,
  Pause,
  RotateCcw,
  Save,
  FileText,
  Clock,
  Shield,
  Calculator,
  Info,
  AlertCircle,
} from 'lucide-vue-next'

interface Props {
  open: boolean
  selectedGrades: GradeEntry[]
  courseId: string
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'completed', result: any): void
  (e: 'cancelled'): void
  (e: 'bulkUpdateReal', bulkData: BulkGradeUpdateRequest): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const assessmentStore = useAssessmentStore()
const { bulkOperationLoading } = storeToRefs(assessmentStore)

// Operation types
type BulkOperationType =
  | 'update_status'
  | 'update_scores'
  | 'apply_bonus'
  | 'apply_penalty'
  | 'update_feedback'
  | 'mark_late_excused'
  | 'update_integrity_status'

interface BulkOperation {
  type: BulkOperationType
  label: string
  description: string
  icon: any
  requiresValue: boolean
  valueType?: 'number' | 'text' | 'select'
  valueOptions?: { value: string; label: string }[]
  requiresReason?: boolean
  validationRules?: {
    min?: number
    max?: number
    required?: boolean
  }
}

const bulkOperations: BulkOperation[] = [
  {
    type: 'update_status',
    label: 'Update Status',
    description: 'Change the status of selected grades',
    icon: CheckCircle,
    requiresValue: true,
    valueType: 'select',
    valueOptions: [
      { value: GradeStatus.DRAFT, label: 'Draft' },
      { value: GradeStatus.PROVISIONAL, label: 'Provisional' },
      { value: GradeStatus.FINAL, label: 'Final' },
    ],
  },
  {
    type: 'update_scores',
    label: 'Update Scores',
    description: 'Set specific scores for selected grades',
    icon: Calculator,
    requiresValue: true,
    valueType: 'number',
    validationRules: { min: 0, max: 100, required: true },
    requiresReason: true,
  },
  {
    type: 'apply_bonus',
    label: 'Apply Bonus Points',
    description: 'Add bonus points to selected grades',
    icon: CheckCircle,
    requiresValue: true,
    valueType: 'number',
    validationRules: { min: 0, max: 50, required: true },
    requiresReason: true,
  },
  {
    type: 'apply_penalty',
    label: 'Apply Penalty',
    description: 'Apply penalty to selected grades',
    icon: AlertTriangle,
    requiresValue: true,
    valueType: 'number',
    validationRules: { min: 0, max: 100, required: true },
    requiresReason: true,
  },
  {
    type: 'update_feedback',
    label: 'Update Feedback',
    description: 'Add or update feedback for selected grades',
    icon: FileText,
    requiresValue: true,
    valueType: 'text',
    validationRules: { required: true },
  },
  {
    type: 'mark_late_excused',
    label: 'Excuse Late Submissions',
    description: 'Mark late submissions as excused',
    icon: Clock,
    requiresValue: false,
    requiresReason: true,
  },
  {
    type: 'update_integrity_status',
    label: 'Update Academic Integrity',
    description: 'Update academic integrity status',
    icon: Shield,
    requiresValue: true,
    valueType: 'select',
    valueOptions: [
      { value: AcademicIntegrityStatus.CLEAR, label: 'Clear' },
      { value: AcademicIntegrityStatus.FLAGGED, label: 'Flagged' },
      { value: AcademicIntegrityStatus.UNDER_REVIEW, label: 'Under Review' },
      { value: AcademicIntegrityStatus.VIOLATION_CONFIRMED, label: 'Violation Confirmed' },
    ],
    requiresReason: true,
  },
]

// Local state
const selectedOperation = ref<BulkOperationType | null>(null)
const operationValue = ref<string | number>('')
const operationReason = ref('')
const previewMode = ref(true)
const validationErrors = ref<string[]>([])
const operationProgress = ref<BulkOperationProgress | null>(null)
const isProcessing = ref(false)
const isPaused = ref(false)
const canCancel = ref(true)
const previewResults = ref<GradeEntry[]>([])

// Computed properties
const currentOperation = computed(() => bulkOperations.find((op) => op.type === selectedOperation.value))

const isValidOperation = computed(() => {
  if (!selectedOperation.value || !currentOperation.value) return false

  validationErrors.value = []

  // Check if value is required and provided
  if (currentOperation.value.requiresValue && !operationValue.value) {
    validationErrors.value.push('Operation value is required')
  }

  // Check if reason is required and provided
  if (currentOperation.value.requiresReason && !operationReason.value.trim()) {
    validationErrors.value.push('Reason is required for this operation')
  }

  // Validate numeric values
  if (currentOperation.value.valueType === 'number' && currentOperation.value.validationRules) {
    const numValue = Number(operationValue.value)
    const rules = currentOperation.value.validationRules

    if (isNaN(numValue)) {
      validationErrors.value.push('Value must be a valid number')
    } else {
      if (rules.min !== undefined && numValue < rules.min) {
        validationErrors.value.push(`Value must be at least ${rules.min}`)
      }
      if (rules.max !== undefined && numValue > rules.max) {
        validationErrors.value.push(`Value must be at most ${rules.max}`)
      }
    }
  }

  return validationErrors.value.length === 0
})

const affectedGradesCount = computed(() => props.selectedGrades.length)

const estimatedDuration = computed(() => {
  // Estimate processing time based on number of grades and operation complexity
  const baseTime = affectedGradesCount.value * 0.1 // 100ms per grade
  const complexityMultiplier = currentOperation.value?.type === 'update_scores' ? 2 : 1
  return Math.ceil(baseTime * complexityMultiplier)
})

const progressPercentage = computed(() => {
  if (!operationProgress.value) return 0
  return Math.round((operationProgress.value.processed / operationProgress.value.total) * 100)
})

const canProceed = computed(() => isValidOperation.value && !isProcessing.value && affectedGradesCount.value > 0)

// Methods
const generatePreview = () => {
  if (!currentOperation.value || !isValidOperation.value) {
    previewResults.value = []
    return
  }

  previewResults.value = props.selectedGrades.map((grade) => {
    const preview = { ...grade }

    switch (selectedOperation.value) {
      case 'update_status':
        preview.status = operationValue.value as GradeStatus
        break

      case 'update_scores':
        const newScore = Number(operationValue.value)
        preview.percentage_score = newScore
        preview.points_earned = (newScore / 100) * grade.max_points
        break

      case 'apply_bonus':
        const bonus = Number(operationValue.value)
        preview.percentage_score = Math.min(100, grade.percentage_score + bonus)
        preview.points_earned = (preview.percentage_score / 100) * grade.max_points
        preview.bonus_points = (preview.bonus_points || 0) + bonus
        break

      case 'apply_penalty':
        const penalty = Number(operationValue.value)
        preview.percentage_score = Math.max(0, grade.percentage_score - penalty)
        preview.points_earned = (preview.percentage_score / 100) * grade.max_points
        preview.late_penalty = (preview.late_penalty || 0) + penalty
        break

      case 'update_feedback':
        preview.feedback = operationValue.value as string
        break

      case 'mark_late_excused':
        preview.is_late = false
        preview.late_penalty = 0
        preview.late_excuse_reason = operationReason.value
        break

      case 'update_integrity_status':
        preview.academic_integrity = operationValue.value as AcademicIntegrityStatus
        break
    }

    return preview
  })
}

const validateBatch = async () => {
  if (!isValidOperation.value) return false

  try {
    // Validate each grade change
    const validationPromises = previewResults.value.map(async (grade) => {
      return await assessmentStore.validateGrade(grade)
    })

    const validationResults = await Promise.all(validationPromises)
    const hasErrors = validationResults.some((result) => !result.is_valid)

    if (hasErrors) {
      validationErrors.value.push('Some grade changes failed validation')
      return false
    }

    return true
  } catch (error) {
    validationErrors.value.push('Validation failed: ' + (error as Error).message)
    return false
  }
}

// New API Method - Real bulk update using the actual API endpoint
const executeOperationReal = async () => {
  if (!canProceed.value) return

  isProcessing.value = true
  canCancel.value = true
  isPaused.value = false

  // Initialize progress tracking
  operationProgress.value = {
    total: affectedGradesCount.value,
    processed: 0,
    successful: 0,
    failed: 0,
    errors: [],
    start_time: new Date().toISOString(),
    estimated_completion: new Date(Date.now() + estimatedDuration.value * 1000).toISOString(),
  }

  try {
    // Prepare bulk update data for the real API
    const bulkScores: BulkScoreUpdate[] = previewResults.value.map((grade) => {
      const scoreUpdate: BulkScoreUpdate = {
        id: grade.id,
      }

      // Map operation type to appropriate fields
      switch (selectedOperation.value) {
        case 'update_scores':
          scoreUpdate.points_earned = grade.points_earned
          scoreUpdate.percentage_score = grade.percentage_score
          break
        case 'update_status':
          scoreUpdate.score_status = grade.status as ScoreStatus
          break
        case 'apply_bonus':
          scoreUpdate.bonus_points = parseFloat(operationValue.value) || 0
          scoreUpdate.bonus_reason = operationReason.value
          break
        case 'update_feedback':
          scoreUpdate.instructor_feedback = operationValue.value
          break
        case 'mark_late_excused':
          scoreUpdate.score_excluded = false // Mark as not excluded if late excused
          break
        default:
          // For other operations, include basic score data
          scoreUpdate.points_earned = grade.points_earned
          scoreUpdate.percentage_score = grade.percentage_score
          break
      }

      return scoreUpdate
    })

    const bulkData: BulkGradeUpdateRequest = {
      scores: bulkScores,
    }

    // Emit the real API event to parent component
    emit('bulkUpdateReal', bulkData)

    // Update progress to show completion
    operationProgress.value.processed = bulkScores.length
    operationProgress.value.successful = bulkScores.length
    operationProgress.value.end_time = new Date().toISOString()

    // Emit completion event
    emit('completed', { success: true, updated: bulkScores.length })

    // Auto-close after success
    setTimeout(() => {
      handleClose()
    }, 2000)
  } catch (error) {
    if (operationProgress.value) {
      operationProgress.value.errors.push({
        grade_id: 0,
        error_message: (error as Error).message,
        error_code: 'BULK_OPERATION_FAILED',
      })
    }
    console.error('Real bulk operation failed:', error)
  } finally {
    isProcessing.value = false
    canCancel.value = false
  }
}

// Legacy Method (for backward compatibility)
const executeOperation = async () => {
  if (!canProceed.value) return

  isProcessing.value = true
  canCancel.value = true
  isPaused.value = false

  // Initialize progress tracking
  operationProgress.value = {
    total: affectedGradesCount.value,
    processed: 0,
    successful: 0,
    failed: 0,
    errors: [],
    start_time: new Date().toISOString(),
    estimated_completion: new Date(Date.now() + estimatedDuration.value * 1000).toISOString(),
  }

  try {
    // Validate batch before processing
    const isValid = await validateBatch()
    if (!isValid) {
      throw new Error('Batch validation failed')
    }

    // Prepare bulk update data
    const bulkData: BulkGradeUpdate = {
      operation_type: selectedOperation.value!,
      grade_entries: previewResults.value.map((grade) => ({
        id: grade.id,
        percentage_score: grade.percentage_score,
        points_earned: grade.points_earned,
        status: grade.status,
        feedback: grade.feedback,
        academic_integrity: grade.academic_integrity,
        is_late: grade.is_late,
        late_penalty: grade.late_penalty,
        late_excuse_reason: grade.late_excuse_reason,
        bonus_points: grade.bonus_points,
      })),
      operation_reason: operationReason.value,
      batch_size: 10, // Process in batches of 10
      validate_before_update: true,
    }

    // Execute bulk operation with progress tracking
    const result = await assessmentStore.bulkUpdateGrades(bulkData)

    // Update progress
    operationProgress.value.processed = result.updated_entries.length
    operationProgress.value.successful = result.updated_entries.length
    operationProgress.value.failed = result.failed_entries?.length || 0
    operationProgress.value.errors = result.errors || []
    operationProgress.value.end_time = new Date().toISOString()

    // Emit completion event
    emit('completed', result)

    // Auto-close after success
    setTimeout(() => {
      handleClose()
    }, 2000)
  } catch (error) {
    if (operationProgress.value) {
      operationProgress.value.errors.push({
        grade_id: 0,
        error_message: (error as Error).message,
        error_code: 'BULK_OPERATION_FAILED',
      })
    }
    console.error('Bulk operation failed:', error)
  } finally {
    isProcessing.value = false
    canCancel.value = false
  }
}

const pauseOperation = () => {
  isPaused.value = true
  // Implementation would depend on backend support for pausing operations
}

const resumeOperation = () => {
  isPaused.value = false
  // Implementation would depend on backend support for resuming operations
}

const cancelOperation = () => {
  if (!canCancel.value) return

  isProcessing.value = false
  isPaused.value = false
  operationProgress.value = null
  emit('cancelled')
  handleClose()
}

const rollbackOperation = async () => {
  // Implementation would depend on backend support for rollback
  console.log('Rollback operation requested')
}

const handleClose = () => {
  // Reset state
  selectedOperation.value = null
  operationValue.value = ''
  operationReason.value = ''
  previewMode.value = true
  validationErrors.value = []
  operationProgress.value = null
  isProcessing.value = false
  isPaused.value = false
  canCancel.value = true
  previewResults.value = []

  emit('update:open', false)
}

const handleOperationChange = (operation: BulkOperationType) => {
  selectedOperation.value = operation
  operationValue.value = ''
  operationReason.value = ''
  validationErrors.value = []
  previewResults.value = []
}

// Watch for changes to generate preview
watch(
  [selectedOperation, operationValue, operationReason],
  () => {
    if (previewMode.value) {
      generatePreview()
    }
  },
  { deep: true },
)

// Watch for dialog open state
watch(
  () => props.open,
  (isOpen) => {
    if (isOpen) {
      // Reset state when dialog opens
      selectedOperation.value = null
      operationValue.value = ''
      operationReason.value = ''
      previewMode.value = true
      validationErrors.value = []
      operationProgress.value = null
      isProcessing.value = false
      previewResults.value = []
    }
  },
)
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent
      class="max-w-4xl max-h-[90vh] overflow-y-auto"
      @pointer-down-outside="!isProcessing && handleClose()"
    >
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Users class="h-5 w-5" />
          Bulk Grading Operations
        </DialogTitle>
        <DialogDescription>
          Apply changes to {{ affectedGradesCount }} selected grade{{ affectedGradesCount !== 1 ? 's' : '' }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Operation Selection -->
        <div v-if="!isProcessing" class="space-y-4">
          <div>
            <Label class="text-base font-medium">Select Operation</Label>
            <p class="text-sm text-muted-foreground mb-3">
              Choose the type of bulk operation to perform on the selected grades
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Card
              v-for="operation in bulkOperations"
              :key="operation.type"
              class="cursor-pointer transition-all hover:shadow-md"
              :class="{
                'ring-2 ring-primary': selectedOperation === operation.type,
                'hover:ring-1 hover:ring-muted-foreground': selectedOperation !== operation.type,
              }"
              @click="handleOperationChange(operation.type)"
            >
              <CardContent class="p-4">
                <div class="flex items-start gap-3">
                  <component :is="operation.icon" class="h-5 w-5 mt-0.5 text-primary" />
                  <div class="flex-1">
                    <h3 class="font-medium">{{ operation.label }}</h3>
                    <p class="text-sm text-muted-foreground">{{ operation.description }}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Operation Configuration -->
        <div v-if="selectedOperation && currentOperation && !isProcessing" class="space-y-4">
          <Separator />

          <div>
            <Label class="text-base font-medium">Configure Operation</Label>
            <p class="text-sm text-muted-foreground">
              Provide the required information for the {{ currentOperation.label.toLowerCase() }} operation
            </p>
          </div>

          <!-- Operation Value Input -->
          <div v-if="currentOperation.requiresValue" class="space-y-2">
            <Label :for="`operation-value-${selectedOperation}`">
              {{
                currentOperation.valueType === 'number'
                  ? 'Value'
                  : currentOperation.valueType === 'select'
                    ? 'Option'
                    : 'Text'
              }}
              <span class="text-red-500">*</span>
            </Label>

            <!-- Number Input -->
            <Input
              v-if="currentOperation.valueType === 'number'"
              :id="`operation-value-${selectedOperation}`"
              v-model="operationValue"
              type="number"
              :min="currentOperation.validationRules?.min"
              :max="currentOperation.validationRules?.max"
              :step="selectedOperation === 'update_scores' ? '0.1' : '1'"
              :placeholder="`Enter ${currentOperation.label.toLowerCase()} value`"
              :aria-describedby="`operation-value-help-${selectedOperation}`"
            />

            <!-- Select Input -->
            <Select
              v-else-if="currentOperation.valueType === 'select'"
              :model-value="operationValue as string"
              @update:model-value="operationValue = $event"
            >
              <SelectTrigger :id="`operation-value-${selectedOperation}`">
                <SelectValue :placeholder="`Select ${currentOperation.label.toLowerCase()}`" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="option in currentOperation.valueOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>

            <!-- Text Input -->
            <Textarea
              v-else-if="currentOperation.valueType === 'text'"
              :id="`operation-value-${selectedOperation}`"
              v-model="operationValue"
              :placeholder="`Enter ${currentOperation.label.toLowerCase()}`"
              rows="3"
              :aria-describedby="`operation-value-help-${selectedOperation}`"
            />

            <p :id="`operation-value-help-${selectedOperation}`" class="text-xs text-muted-foreground">
              <template v-if="currentOperation.validationRules">
                <template
                  v-if="
                    currentOperation.validationRules.min !== undefined &&
                    currentOperation.validationRules.max !== undefined
                  "
                >
                  Value must be between {{ currentOperation.validationRules.min }} and
                  {{ currentOperation.validationRules.max }}
                </template>
                <template v-else-if="currentOperation.validationRules.min !== undefined">
                  Value must be at least {{ currentOperation.validationRules.min }}
                </template>
                <template v-else-if="currentOperation.validationRules.max !== undefined">
                  Value must be at most {{ currentOperation.validationRules.max }}
                </template>
              </template>
            </p>
          </div>

          <!-- Operation Reason -->
          <div v-if="currentOperation.requiresReason" class="space-y-2">
            <Label for="operation-reason">
              Reason
              <span class="text-red-500">*</span>
            </Label>
            <Textarea
              id="operation-reason"
              v-model="operationReason"
              placeholder="Provide a reason for this bulk operation"
              rows="2"
              aria-describedby="operation-reason-help"
            />
            <p id="operation-reason-help" class="text-xs text-muted-foreground">
              This reason will be recorded in the audit log for all affected grades
            </p>
          </div>

          <!-- Validation Errors -->
          <Alert v-if="validationErrors.length > 0" variant="destructive">
            <AlertTriangle class="h-4 w-4" />
            <AlertDescription>
              <ul class="list-disc list-inside space-y-1">
                <li v-for="error in validationErrors" :key="error">{{ error }}</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <!-- Preview Results -->
        <div v-if="previewResults.length > 0 && !isProcessing" class="space-y-4">
          <Separator />

          <div class="flex items-center justify-between">
            <div>
              <Label class="text-base font-medium">Preview Changes</Label>
              <p class="text-sm text-muted-foreground">
                Review the changes that will be applied to {{ previewResults.length }} grade{{
                  previewResults.length !== 1 ? 's' : ''
                }}
              </p>
            </div>
            <Badge variant="outline" class="gap-1">
              <Info class="h-3 w-3" />
              {{ previewResults.length }} changes
            </Badge>
          </div>

          <Card class="max-h-60 overflow-y-auto">
            <CardContent class="p-0">
              <div class="divide-y">
                <div
                  v-for="(grade, index) in previewResults.slice(0, 10)"
                  :key="grade.id"
                  class="p-3 flex items-center justify-between"
                >
                  <div class="flex-1">
                    <p class="font-medium">{{ grade.student_name }}</p>
                    <p class="text-sm text-muted-foreground">{{ grade.student_email }}</p>
                  </div>
                  <div class="text-right">
                    <template v-if="selectedOperation === 'update_scores'">
                      <p class="text-sm">
                        <span class="line-through text-muted-foreground">
                          {{ props.selectedGrades[index].percentage_score }}%
                        </span>
                        →
                        <span class="font-medium">{{ grade.percentage_score }}%</span>
                      </p>
                    </template>
                    <template v-else-if="selectedOperation === 'apply_bonus'">
                      <p class="text-sm">
                        <span class="text-muted-foreground">{{ props.selectedGrades[index].percentage_score }}%</span>
                        →
                        <span class="font-medium text-green-600">{{ grade.percentage_score }}%</span>
                        <span class="text-xs text-green-600">(+{{ operationValue }})</span>
                      </p>
                    </template>
                    <template v-else-if="selectedOperation === 'apply_penalty'">
                      <p class="text-sm">
                        <span class="text-muted-foreground">{{ props.selectedGrades[index].percentage_score }}%</span>
                        →
                        <span class="font-medium text-red-600">{{ grade.percentage_score }}%</span>
                        <span class="text-xs text-red-600">(-{{ operationValue }})</span>
                      </p>
                    </template>
                    <template v-else-if="selectedOperation === 'update_status'">
                      <Badge :variant="grade.status === GradeStatus.FINAL ? 'default' : 'secondary'">
                        {{ grade.status }}
                      </Badge>
                    </template>
                    <template v-else>
                      <Badge variant="outline">Updated</Badge>
                    </template>
                  </div>
                </div>

                <div v-if="previewResults.length > 10" class="p-3 text-center text-sm text-muted-foreground">
                  ... and {{ previewResults.length - 10 }} more grades
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Operation Progress -->
        <div v-if="isProcessing && operationProgress" class="space-y-4">
          <Separator />

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <Label class="text-base font-medium">Processing Operation</Label>
                <p class="text-sm text-muted-foreground">{{ currentOperation?.label }} in progress...</p>
              </div>
              <div class="flex items-center gap-2">
                <Button
                  v-if="!isPaused && canCancel"
                  variant="outline"
                  size="sm"
                  @click="pauseOperation"
                  :disabled="!canCancel"
                >
                  <Pause class="h-4 w-4 mr-1" />
                  Pause
                </Button>
                <Button v-if="isPaused" variant="outline" size="sm" @click="resumeOperation">
                  <Play class="h-4 w-4 mr-1" />
                  Resume
                </Button>
                <Button variant="destructive" size="sm" @click="cancelOperation" :disabled="!canCancel">
                  <X class="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span>Progress: {{ operationProgress.processed }} / {{ operationProgress.total }}</span>
                <span>{{ progressPercentage }}%</span>
              </div>
              <Progress :value="progressPercentage" class="h-2" />
            </div>

            <!-- Progress Stats -->
            <div class="grid grid-cols-3 gap-4">
              <Card>
                <CardContent class="p-3 text-center">
                  <div class="text-2xl font-bold text-green-600">{{ operationProgress.successful }}</div>
                  <div class="text-xs text-muted-foreground">Successful</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent class="p-3 text-center">
                  <div class="text-2xl font-bold text-red-600">{{ operationProgress.failed }}</div>
                  <div class="text-xs text-muted-foreground">Failed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent class="p-3 text-center">
                  <div class="text-2xl font-bold">{{ operationProgress.total - operationProgress.processed }}</div>
                  <div class="text-xs text-muted-foreground">Remaining</div>
                </CardContent>
              </Card>
            </div>

            <!-- Errors -->
            <div v-if="operationProgress.errors.length > 0" class="space-y-2">
              <Label class="text-sm font-medium text-red-600">Errors</Label>
              <Card class="max-h-32 overflow-y-auto">
                <CardContent class="p-3">
                  <div class="space-y-2">
                    <div
                      v-for="error in operationProgress.errors"
                      :key="`${error.grade_id}-${error.error_code}`"
                      class="text-sm"
                    >
                      <span class="font-medium">Grade {{ error.grade_id }}:</span>
                      <span class="text-red-600">{{ error.error_message }}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        <!-- Completion Summary -->
        <div v-if="operationProgress?.end_time" class="space-y-4">
          <Separator />

          <Alert>
            <CheckCircle class="h-4 w-4" />
            <AlertDescription>
              <div class="space-y-2">
                <p class="font-medium">Operation completed successfully!</p>
                <div class="text-sm">
                  <p>{{ operationProgress.successful }} grades updated successfully</p>
                  <p v-if="operationProgress.failed > 0" class="text-red-600">
                    {{ operationProgress.failed }} grades failed to update
                  </p>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <div class="flex items-center gap-2">
            <Button variant="outline" @click="rollbackOperation" :disabled="operationProgress.successful === 0">
              <RotateCcw class="h-4 w-4 mr-1" />
              Rollback Changes
            </Button>
          </div>
        </div>
      </div>

      <DialogFooter v-if="!isProcessing">
        <Button variant="outline" @click="handleClose">Cancel</Button>
        <Button @click="executeOperationReal" :disabled="!canProceed" class="gap-2">
          <LoadingSpinner v-if="bulkOperationLoading" size="sm" />
          <Save v-else class="h-4 w-4" />
          Apply Changes (Real API)
        </Button>
        <Button @click="executeOperation" :disabled="!canProceed" class="gap-2" variant="outline">
          <LoadingSpinner v-if="bulkOperationLoading" size="sm" />
          <Save v-else class="h-4 w-4" />
          Apply Changes (Legacy)
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
/* Ensure proper scrolling in dialog */
.max-h-60 {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.max-h-60::-webkit-scrollbar {
  width: 6px;
}

.max-h-60::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.max-h-60::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

/* Focus indicators for accessibility */
button:focus,
input:focus,
textarea:focus,
[role='button']:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border {
    border-width: 2px;
  }

  .ring-2 {
    ring-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .transition-all {
    transition: none;
  }

  .animate-spin {
    animation: none;
  }
}
</style>
