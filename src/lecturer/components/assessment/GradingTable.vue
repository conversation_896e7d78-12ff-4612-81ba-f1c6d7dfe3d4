<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { storeToRefs } from 'pinia'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select'
import { Badge } from '@/shared/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Textarea } from '@/shared/components/ui/textarea'
import { Separator } from '@/shared/components/ui/separator'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import BulkGradingModal from './BulkGradingModal.vue'
import { useAssessmentStore } from '@/lecturer/stores/assessment'
import {
  useKeyboardShortcuts,
  createGradingShortcuts,
  useFocusManagement,
} from '@/lecturer/composables/useKeyboardShortcuts'
import { GradingMode, GradeStatus, AcademicIntegrityStatus } from '@/lecturer/types/models/assessment'
import type {
  GradeEntry,
  Assessment,
  AssessmentDetail,
  StudentGradingData,
  ComponentGradingData,
  Score,
  GradeUpdateRequest,
} from '@/lecturer/types/models/assessment'
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Search,
  Filter,
  Save,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  FileText,
  Eye,
  EyeOff,
  MoreHorizontal,
  Edit3,
  Users,
  X,
} from 'lucide-vue-next'

interface Props {
  courseId: string
  mode: GradingMode
  selectedAssessmentId?: number
  selectedStudentId?: number
  studentGradingData?: StudentGradingData | null
  componentGradingData?: ComponentGradingData | null
}

interface Emits {
  (e: 'gradeChange', gradeId: number, grade: GradeEntry): void
  (e: 'gradeChangeReal', scoreId: number, gradeData: GradeUpdateRequest): void
  (e: 'loadStudentData', studentId: number): void
  (e: 'loadComponentData', assessmentComponentId: number): void
  (e: 'bulkUpdateReal', bulkData: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const assessmentStore = useAssessmentStore()
const {
  grades,
  filteredGrades,
  assessments,
  selectedAssessment,
  gradeFilters,
  loading,
  gradesLoading,
  hasUnsavedChanges,
} = storeToRefs(assessmentStore)

// Virtual scrolling setup
const parentRef = ref<HTMLElement>()
const scrollElementRef = ref<HTMLElement>()

// Table state
const sortBy = ref<string>('student_name')
const sortOrder = ref<'asc' | 'desc'>('asc')
const searchQuery = ref('')
const showFilters = ref(false)
const selectedRows = ref<Set<number>>(new Set())
const editingCell = ref<{ gradeId: number; field: string } | null>(null)
const pendingChanges = ref<Map<number, Partial<GradeEntry>>>(new Map())
const showBulkModal = ref(false)

// Keyboard navigation and focus management
const keyboardShortcuts = useKeyboardShortcuts()
const focusManagement = useFocusManagement()
const currentFocusedCell = ref<{ row: number; col: number } | null>(null)
const isNavigatingWithKeyboard = ref(false)

// Mobile responsive state
const isMobile = ref(false)
const visibleColumns = ref<Set<string>>(
  new Set(['student_name', 'points_earned', 'percentage_score', 'status', 'actions']),
)

// Column definitions
const columnDefinitions = [
  {
    key: 'student_name',
    label: 'Student',
    sortable: true,
    width: 200,
    minWidth: 150,
    priority: 1,
    mobile: true,
  },
  {
    key: 'student_email',
    label: 'Email',
    sortable: true,
    width: 180,
    minWidth: 150,
    priority: 3,
    mobile: false,
  },
  {
    key: 'points_earned',
    label: 'Points',
    sortable: true,
    width: 100,
    minWidth: 80,
    priority: 1,
    mobile: true,
    type: 'number',
  },
  {
    key: 'percentage_score',
    label: 'Score (%)',
    sortable: true,
    width: 100,
    minWidth: 80,
    priority: 1,
    mobile: true,
    type: 'percentage',
  },
  {
    key: 'letter_grade',
    label: 'Grade',
    sortable: true,
    width: 80,
    minWidth: 60,
    priority: 2,
    mobile: true,
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true,
    width: 120,
    minWidth: 100,
    priority: 1,
    mobile: true,
  },
  {
    key: 'submission_date',
    label: 'Submitted',
    sortable: true,
    width: 120,
    minWidth: 100,
    priority: 4,
    mobile: false,
    type: 'date',
  },
  {
    key: 'is_late',
    label: 'Late',
    sortable: true,
    width: 80,
    minWidth: 60,
    priority: 3,
    mobile: false,
    type: 'boolean',
  },
  {
    key: 'academic_integrity',
    label: 'Integrity',
    sortable: true,
    width: 100,
    minWidth: 80,
    priority: 4,
    mobile: false,
  },
  {
    key: 'actions',
    label: 'Actions',
    sortable: false,
    width: 120,
    minWidth: 100,
    priority: 1,
    mobile: true,
  },
]

// Computed properties
const sortedAndFilteredGrades = computed(() => {
  let result = [...filteredGrades.value]

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(
      (grade) => grade.student_name.toLowerCase().includes(query) || grade.student_email.toLowerCase().includes(query),
    )
  }

  // Apply sorting
  result.sort((a, b) => {
    let aValue = a[sortBy.value as keyof GradeEntry]
    let bValue = b[sortBy.value as keyof GradeEntry]

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0
    if (aValue == null) return sortOrder.value === 'asc' ? 1 : -1
    if (bValue == null) return sortOrder.value === 'asc' ? -1 : 1

    // Convert to comparable values
    if (typeof aValue === 'string') aValue = aValue.toLowerCase()
    if (typeof bValue === 'string') bValue = bValue.toLowerCase()

    let comparison = 0
    if (aValue < bValue) comparison = -1
    if (aValue > bValue) comparison = 1

    return sortOrder.value === 'desc' ? -comparison : comparison
  })

  return result
})

const displayColumns = computed(() => {
  if (isMobile.value) {
    return columnDefinitions.filter((col) => col.mobile)
  }
  return columnDefinitions.filter((col) => visibleColumns.value.has(col.key))
})

const totalWidth = computed(() => {
  return displayColumns.value.reduce((sum, col) => sum + col.width, 0)
})

const selectedGrades = computed(() => {
  return Array.from(selectedRows.value)
    .map((id) => sortedAndFilteredGrades.value.find((g) => g.id === id))
    .filter(Boolean) as GradeEntry[]
})

// New API Data Computed Properties
const apiGradesData = computed(() => {
  if (props.mode === GradingMode.BY_STUDENT && props.studentGradingData) {
    // Convert StudentGradingData to display format
    const student = props.studentGradingData.student
    const grades: any[] = []

    props.studentGradingData.assessments.forEach((assessment) => {
      assessment.details.forEach((detail) => {
        if (detail.score) {
          grades.push({
            id: detail.score.id,
            student_id: student.id,
            student_name: student.name,
            student_email: student.email,
            assessment_name: assessment.name,
            assessment_detail_name: detail.name,
            points_earned: detail.score.points_earned,
            percentage_score: detail.score.percentage_score,
            letter_grade: detail.score.letter_grade,
            status: detail.score.status,
            score_status: detail.score.score_status,
            is_late: detail.score.is_late,
            instructor_feedback: detail.score.instructor_feedback,
            max_points: detail.max_points,
            weight: detail.weight,
            due_date: detail.due_date,
            score: detail.score,
          })
        }
      })
    })

    return grades
  } else if (props.mode === GradingMode.BY_COMPONENT && props.componentGradingData) {
    // Convert ComponentGradingData to display format
    const grades: any[] = []

    props.componentGradingData.details.forEach((detail) => {
      detail.student_scores.forEach((studentScore) => {
        grades.push({
          id: studentScore.score.id,
          student_id: studentScore.student.id,
          student_name: studentScore.student.name,
          student_email: studentScore.student.email,
          assessment_name: props.componentGradingData?.assessment_component.name,
          assessment_detail_name: detail.name,
          points_earned: studentScore.score.points_earned,
          percentage_score: studentScore.score.percentage_score,
          letter_grade: studentScore.score.letter_grade,
          status: studentScore.score.status,
          score_status: studentScore.score.score_status,
          is_late: studentScore.score.is_late,
          instructor_feedback: studentScore.score.instructor_feedback,
          max_points: detail.max_points,
          weight: detail.weight,
          due_date: detail.due_date,
          score: studentScore.score,
        })
      })
    })

    return grades
  }

  return []
})

// Use API data if available, otherwise fall back to legacy data
const displayGrades = computed(() => {
  return apiGradesData.value.length > 0 ? apiGradesData.value : sortedAndFilteredGrades.value
})

// Virtual scrolling setup
const virtualizer = computed(() => {
  if (!parentRef.value) return null

  return useVirtualizer({
    count: displayGrades.value.length,
    getScrollElement: () => scrollElementRef.value,
    estimateSize: () => 60, // Row height
    overscan: 10,
  })
})

// Status badge variants
const getStatusVariant = (status: GradeStatus) => {
  switch (status) {
    case GradeStatus.FINAL:
      return 'default'
    case GradeStatus.PROVISIONAL:
      return 'secondary'
    case GradeStatus.DRAFT:
      return 'outline'
    case GradeStatus.PENDING:
      return 'destructive'
    default:
      return 'outline'
  }
}

const getStatusIcon = (status: GradeStatus) => {
  switch (status) {
    case GradeStatus.FINAL:
      return CheckCircle
    case GradeStatus.PROVISIONAL:
      return Clock
    case GradeStatus.DRAFT:
      return Edit3
    case GradeStatus.PENDING:
      return AlertTriangle
    default:
      return Clock
  }
}

const getIntegrityColor = (status: AcademicIntegrityStatus) => {
  switch (status) {
    case AcademicIntegrityStatus.CLEAR:
      return 'text-green-600'
    case AcademicIntegrityStatus.FLAGGED:
      return 'text-yellow-600'
    case AcademicIntegrityStatus.VIOLATION_CONFIRMED:
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

// Methods
const handleSort = (column: string) => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column
    sortOrder.value = 'asc'
  }
}

const getSortIcon = (column: string) => {
  if (sortBy.value !== column) return ArrowUpDown
  return sortOrder.value === 'asc' ? ArrowUp : ArrowDown
}

// New API Methods
const handleCellEditReal = (scoreId: number, field: string, value: any) => {
  // Create grade update request for the real API
  const gradeData: GradeUpdateRequest = {}

  switch (field) {
    case 'points_earned':
      gradeData.points_earned = parseFloat(value) || 0
      break
    case 'percentage_score':
      gradeData.percentage_score = parseFloat(value) || 0
      break
    case 'letter_grade':
      gradeData.letter_grade = value
      break
    case 'instructor_feedback':
      gradeData.instructor_feedback = value
      break
    case 'bonus_points':
      gradeData.bonus_points = parseFloat(value) || 0
      break
    case 'score_excluded':
      gradeData.score_excluded = Boolean(value)
      break
    default:
      console.warn(`Unknown field for grade update: ${field}`)
      return
  }

  // Emit the real API event
  emit('gradeChangeReal', scoreId, gradeData)
}

const handleLoadStudentData = (studentId: number) => {
  emit('loadStudentData', studentId)
}

const handleLoadComponentData = (assessmentComponentId: number) => {
  emit('loadComponentData', assessmentComponentId)
}

const handleBulkUpdateReal = (bulkData: any) => {
  emit('bulkUpdateReal', bulkData)
}

// Legacy Methods (for backward compatibility)
const handleCellEdit = (gradeId: number, field: string, value: any) => {
  const currentChanges = pendingChanges.value.get(gradeId) || {}
  const updatedChanges = { ...currentChanges, [field]: value }
  pendingChanges.value.set(gradeId, updatedChanges)

  // Find the original grade and create updated version
  const originalGrade = grades.value.find((g) => g.id === gradeId)
  if (originalGrade) {
    const updatedGrade = { ...originalGrade, ...updatedChanges }
    assessmentStore.addUnsavedChange(updatedGrade)

    // Emit change event for parent component to handle validation
    emit('gradeChange', gradeId, updatedGrade)
  }

  // Also emit the real API event if we have score data
  const displayGrade = displayGrades.value.find((g) => g.id === gradeId)
  if (displayGrade?.score) {
    handleCellEditReal(displayGrade.score.id, field, value)
  }
}

const startCellEdit = (gradeId: number, field: string) => {
  editingCell.value = { gradeId, field }

  nextTick(() => {
    const input = document.querySelector(`[data-cell-edit="${gradeId}-${field}"]`) as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

const finishCellEdit = () => {
  editingCell.value = null
}

const handleKeyDown = (event: KeyboardEvent, gradeId: number, field: string) => {
  isNavigatingWithKeyboard.value = true

  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      finishCellEdit()
      // Move to next row, same column
      moveToNextCell(gradeId, field, 'down')
      announceNavigation('Moved to next row')
      break
    case 'Tab':
      event.preventDefault()
      finishCellEdit()
      // Move to next column or next row
      moveToNextCell(gradeId, field, event.shiftKey ? 'left' : 'right')
      announceNavigation(event.shiftKey ? 'Moved to previous field' : 'Moved to next field')
      break
    case 'Escape':
      event.preventDefault()
      // Cancel edit and revert changes
      pendingChanges.value.delete(gradeId)
      finishCellEdit()
      announceNavigation('Edit cancelled')
      break
    case 'ArrowUp':
      if (!editingCell.value) {
        event.preventDefault()
        moveToNextCell(gradeId, field, 'up')
        announceNavigation('Moved up one row')
      }
      break
    case 'ArrowDown':
      if (!editingCell.value) {
        event.preventDefault()
        moveToNextCell(gradeId, field, 'down')
        announceNavigation('Moved down one row')
      }
      break
    case 'ArrowLeft':
      if (!editingCell.value) {
        event.preventDefault()
        moveToNextCell(gradeId, field, 'left')
        announceNavigation('Moved to previous column')
      }
      break
    case 'ArrowRight':
      if (!editingCell.value) {
        event.preventDefault()
        moveToNextCell(gradeId, field, 'right')
        announceNavigation('Moved to next column')
      }
      break
    case ' ':
      if (!editingCell.value) {
        event.preventDefault()
        toggleRowSelection(gradeId)
        const isSelected = selectedRows.value.has(gradeId)
        announceNavigation(isSelected ? 'Row selected' : 'Row deselected')
      }
      break
  }
}

const moveToNextCell = (currentGradeId: number, currentField: string, direction: 'up' | 'down' | 'left' | 'right') => {
  const currentIndex = sortedAndFilteredGrades.value.findIndex((g) => g.id === currentGradeId)
  const currentColumnIndex = displayColumns.value.findIndex((col) => col.key === currentField)

  let nextGradeId = currentGradeId
  let nextField = currentField

  switch (direction) {
    case 'up':
      if (currentIndex > 0) {
        nextGradeId = sortedAndFilteredGrades.value[currentIndex - 1].id
      }
      break
    case 'down':
      if (currentIndex < sortedAndFilteredGrades.value.length - 1) {
        nextGradeId = sortedAndFilteredGrades.value[currentIndex + 1].id
      }
      break
    case 'left':
      if (currentColumnIndex > 0) {
        const editableColumns = displayColumns.value.filter((col) => col.type)
        const editableIndex = editableColumns.findIndex((col) => col.key === currentField)
        if (editableIndex > 0) {
          nextField = editableColumns[editableIndex - 1].key
        }
      }
      break
    case 'right':
      const editableColumns = displayColumns.value.filter((col) => col.type)
      const editableIndex = editableColumns.findIndex((col) => col.key === currentField)
      if (editableIndex < editableColumns.length - 1) {
        nextField = editableColumns[editableIndex + 1].key
      } else if (currentIndex < sortedAndFilteredGrades.value.length - 1) {
        nextGradeId = sortedAndFilteredGrades.value[currentIndex + 1].id
        nextField = editableColumns[0].key
      }
      break
  }

  if (nextGradeId !== currentGradeId || nextField !== currentField) {
    if (isNavigatingWithKeyboard.value) {
      focusCell(nextGradeId, nextField)
    } else {
      startCellEdit(nextGradeId, nextField)
    }
  }
}

const openBulkGradingModal = () => {
  if (selectedRows.value.size === 0) return
  showBulkModal.value = true
}

const handleBulkOperationCompleted = (result: any) => {
  console.log('Bulk operation completed:', result)
  selectedRows.value.clear()
  showBulkModal.value = false
}

const handleBulkOperationCancelled = () => {
  showBulkModal.value = false
}

const clearSelection = () => {
  selectedRows.value.clear()
}

const announceNavigation = (message: string) => {
  focusManagement.announceToScreenReader(message, 'polite')
}

const setupTableKeyboardShortcuts = () => {
  const shortcuts = createGradingShortcuts({
    openBulkActions: () => {
      if (selectedRows.value.size > 0) {
        openBulkGradingModal()
      }
    },
    focusSearch: () => {
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        announceNavigation('Search input focused')
      }
    },
    markFinal: () => {
      if (selectedRows.value.size > 0) {
        handleQuickStatusChange(GradeStatus.FINAL)
      }
    },
    markProvisional: () => {
      if (selectedRows.value.size > 0) {
        handleQuickStatusChange(GradeStatus.PROVISIONAL)
      }
    },
    markDraft: () => {
      if (selectedRows.value.size > 0) {
        handleQuickStatusChange(GradeStatus.DRAFT)
      }
    },
  })

  shortcuts.forEach((shortcut) => {
    keyboardShortcuts.registerShortcut(shortcut)
  })
}

const handleQuickStatusChange = async (status: GradeStatus) => {
  const selectedGrades = Array.from(selectedRows.value)
    .map((id) => sortedAndFilteredGrades.value.find((g) => g.id === id))
    .filter(Boolean) as GradeEntry[]

  if (selectedGrades.length === 0) return

  try {
    await assessmentStore.bulkUpdateGrades({
      grade_entries: selectedGrades.map((g) => ({ id: g.id, status })),
      operation_type: 'update_status',
    })

    announceNavigation(`${selectedGrades.length} grades marked as ${status}`)
    selectedRows.value.clear()
  } catch (error) {
    announceNavigation('Failed to update grade status')
    console.error('Quick status change failed:', error)
  }
}

const handleSelectAll = () => {
  if (selectedRows.value.size === sortedAndFilteredGrades.value.length) {
    selectedRows.value.clear()
    announceNavigation('All rows deselected')
  } else {
    selectedRows.value = new Set(sortedAndFilteredGrades.value.map((g) => g.id))
    announceNavigation(`All ${sortedAndFilteredGrades.value.length} rows selected`)
  }
}

// Enhanced cell navigation with better focus management
const focusCell = (gradeId: number, field: string) => {
  nextTick(() => {
    const cellSelector = `[data-cell="${gradeId}-${field}"]`
    const cell = document.querySelector(cellSelector) as HTMLElement
    if (cell) {
      cell.focus()

      // Update current focus position
      const rowIndex = sortedAndFilteredGrades.value.findIndex((g) => g.id === gradeId)
      const colIndex = displayColumns.value.findIndex((col) => col.key === field)
      currentFocusedCell.value = { row: rowIndex, col: colIndex }
    }
  })
}

const toggleRowSelection = (gradeId: number) => {
  if (selectedRows.value.has(gradeId)) {
    selectedRows.value.delete(gradeId)
  } else {
    selectedRows.value.add(gradeId)
  }
}

const toggleAllSelection = () => {
  if (selectedRows.value.size === sortedAndFilteredGrades.value.length) {
    selectedRows.value.clear()
  } else {
    selectedRows.value = new Set(sortedAndFilteredGrades.value.map((g) => g.id))
  }
}

const formatValue = (value: unknown, type?: string) => {
  if (value == null) return '-'

  switch (type) {
    case 'number':
      return typeof value === 'number' ? value.toFixed(1) : value
    case 'percentage':
      return typeof value === 'number' ? `${value.toFixed(1)}%` : value
    case 'date':
      return value ? new Date(value).toLocaleDateString() : '-'
    case 'boolean':
      return value ? 'Yes' : 'No'
    default:
      return value
  }
}

const checkResponsive = () => {
  isMobile.value = window.innerWidth < 768

  if (isMobile.value) {
    // Show only essential columns on mobile
    visibleColumns.value = new Set(['student_name', 'percentage_score', 'status', 'actions'])
  } else {
    // Show all columns on desktop
    visibleColumns.value = new Set(columnDefinitions.map((col) => col.key))
  }
}

// Lifecycle
onMounted(() => {
  checkResponsive()
  window.addEventListener('resize', checkResponsive)
  setupTableKeyboardShortcuts()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkResponsive)
  keyboardShortcuts.clearShortcuts()
})

// Watch for filter changes
watch(searchQuery, (newQuery) => {
  assessmentStore.setGradeFilters({ search_query: newQuery })
})
</script>

<template>
  <div class="space-y-4">
    <!-- Skip Links for Accessibility -->
    <div class="sr-only">
      <a href="#table-controls" class="skip-link">Skip to table controls</a>
      <a href="#grade-table" class="skip-link">Skip to grade table</a>
      <a href="#bulk-actions" class="skip-link">Skip to bulk actions</a>
    </div>
    <!-- Table Controls -->
    <div id="table-controls" class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      <!-- Search and Filters -->
      <div class="flex items-center gap-2 flex-1 max-w-md">
        <div class="relative flex-1">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchQuery"
            placeholder="Search students..."
            class="pl-10"
            data-search-input
            aria-label="Search students by name or email"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          @click="showFilters = !showFilters"
          :aria-expanded="showFilters"
          aria-label="Toggle filters"
        >
          <Filter class="h-4 w-4" />
        </Button>
      </div>

      <!-- Bulk Actions -->
      <div id="bulk-actions" v-if="selectedRows.size > 0" class="flex items-center gap-2">
        <span class="text-sm text-muted-foreground">{{ selectedRows.size }} selected</span>
        <Button variant="outline" size="sm" @click="openBulkGradingModal" :disabled="selectedRows.size === 0">
          <Users class="h-4 w-4 mr-1" />
          Bulk Actions
        </Button>
        <Button variant="ghost" size="sm" @click="clearSelection" :disabled="selectedRows.size === 0">
          <X class="h-4 w-4 mr-1" />
          Clear
        </Button>
      </div>
    </div>

    <!-- Mobile Card Layout -->
    <div v-if="isMobile" class="space-y-3">
      <Card v-for="grade in sortedAndFilteredGrades" :key="grade.id" class="p-4">
        <div class="space-y-3">
          <!-- Student Info -->
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium">{{ grade.student_name }}</h3>
              <p class="text-sm text-muted-foreground">{{ grade.student_email }}</p>
            </div>
            <Checkbox
              :checked="selectedRows.has(grade.id)"
              @update:checked="toggleRowSelection(grade.id)"
              :aria-label="`Select ${grade.student_name}`"
            />
          </div>

          <!-- Grade Info -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-xs font-medium text-muted-foreground">Score</label>
              <div class="flex items-center gap-2">
                <Input
                  v-if="editingCell?.gradeId === grade.id && editingCell?.field === 'percentage_score'"
                  :data-cell-edit="`${grade.id}-percentage_score`"
                  :model-value="pendingChanges.get(grade.id)?.percentage_score ?? grade.percentage_score"
                  @update:model-value="
                    (value) => handleCellEdit(grade.id, 'percentage_score', parseFloat(value as string))
                  "
                  @blur="finishCellEdit"
                  @keydown="(e) => handleKeyDown(e, grade.id, 'percentage_score')"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  class="h-8"
                />
                <button
                  v-else
                  @click="startCellEdit(grade.id, 'percentage_score')"
                  class="text-left hover:bg-gray-50 p-1 rounded"
                  :aria-label="`Edit score for ${grade.student_name}`"
                >
                  {{ formatValue(grade.percentage_score, 'percentage') }}
                </button>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium text-muted-foreground">Status</label>
              <div>
                <Select
                  :model-value="pendingChanges.get(grade.id)?.status ?? grade.status"
                  @update:model-value="(value) => handleCellEdit(grade.id, 'status', value)"
                >
                  <SelectTrigger class="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem :value="GradeStatus.DRAFT">Draft</SelectItem>
                    <SelectItem :value="GradeStatus.PROVISIONAL">Provisional</SelectItem>
                    <SelectItem :value="GradeStatus.FINAL">Final</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <!-- Additional Info -->
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center gap-2">
              <Badge :variant="getStatusVariant(grade.status)" class="gap-1">
                <component :is="getStatusIcon(grade.status)" class="h-3 w-3" />
                {{ grade.status }}
              </Badge>
              <Badge v-if="grade.is_late" variant="destructive" class="gap-1">
                <Clock class="h-3 w-3" />
                Late
              </Badge>
            </div>
            <Button variant="ghost" size="sm">
              <MoreHorizontal class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>

    <!-- Desktop Table Layout -->
    <Card id="grade-table" v-else>
      <div
        ref="parentRef"
        class="relative overflow-auto"
        style="height: 600px"
        role="table"
        aria-label="Student grades table"
      >
        <!-- Table Header -->
        <div class="sticky top-0 z-10 bg-white border-b" role="rowgroup">
          <div class="flex items-center h-12 px-4" :style="{ width: `${totalWidth}px` }" role="row">
            <!-- Select All Checkbox -->
            <div class="w-12 flex items-center justify-center">
              <Checkbox
                :checked="selectedRows.size === displayGrades.length && displayGrades.length > 0"
                :indeterminate="selectedRows.size > 0 && selectedRows.size < displayGrades.length"
                @update:checked="toggleAllSelection"
                aria-label="Select all rows"
              />
            </div>

            <!-- Column Headers -->
            <div
              v-for="column in displayColumns"
              :key="column.key"
              :style="{ width: `${column.width}px`, minWidth: `${column.minWidth}px` }"
              class="flex items-center gap-2 px-2 text-sm font-medium"
              role="columnheader"
              :aria-sort="sortBy === column.key ? (sortOrder === 'asc' ? 'ascending' : 'descending') : 'none'"
            >
              <button
                v-if="column.sortable"
                @click="handleSort(column.key)"
                class="flex items-center gap-1 hover:text-primary"
                :aria-label="`Sort by ${column.label}`"
              >
                {{ column.label }}
                <component :is="getSortIcon(column.key)" class="h-4 w-4" />
              </button>
              <span v-else>{{ column.label }}</span>
            </div>
          </div>
        </div>

        <!-- Virtual Scrolling Container -->
        <div ref="scrollElementRef" class="overflow-auto" style="height: calc(100% - 48px)">
          <div
            v-if="virtualizer"
            :style="{ height: `${virtualizer.getTotalSize()}px`, position: 'relative' }"
            role="rowgroup"
          >
            <!-- Virtual Rows -->
            <div
              v-for="virtualRow in virtualizer.getVirtualItems()"
              :key="virtualRow.key"
              :style="{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`,
              }"
            >
              <div
                v-if="displayGrades[virtualRow.index]"
                class="flex items-center h-full px-4 border-b hover:bg-gray-50"
                :class="{ 'bg-blue-50': selectedRows.has(displayGrades[virtualRow.index].id) }"
                :style="{ width: `${totalWidth}px` }"
                role="row"
                :aria-selected="selectedRows.has(displayGrades[virtualRow.index].id)"
              >
                <!-- Row Selection -->
                <div class="w-12 flex items-center justify-center">
                  <Checkbox
                    :checked="selectedRows.has(displayGrades[virtualRow.index].id)"
                    @update:checked="toggleRowSelection(displayGrades[virtualRow.index].id)"
                    :aria-label="`Select ${displayGrades[virtualRow.index].student_name}`"
                  />
                </div>

                <!-- Row Cells -->
                <div
                  v-for="column in displayColumns"
                  :key="column.key"
                  :style="{ width: `${column.width}px`, minWidth: `${column.minWidth}px` }"
                  class="px-2 text-sm"
                  role="cell"
                >
                  <!-- Editable Cells -->
                  <template v-if="column.type && column.key !== 'actions'">
                    <Input
                      v-if="
                        editingCell?.gradeId === displayGrades[virtualRow.index].id && editingCell?.field === column.key
                      "
                      :data-cell-edit="`${displayGrades[virtualRow.index].id}-${column.key}`"
                      :model-value="
                        pendingChanges.get(displayGrades[virtualRow.index].id)?.[column.key as keyof GradeEntry] ??
                        displayGrades[virtualRow.index][column.key as keyof GradeEntry]
                      "
                      @update:model-value="
                        (value) =>
                          handleCellEdit(
                            displayGrades[virtualRow.index].id,
                            column.key,
                            column.type === 'number' || column.type === 'percentage'
                              ? parseFloat(value as string)
                              : value,
                          )
                      "
                      @blur="finishCellEdit"
                      @keydown="(e) => handleKeyDown(e, displayGrades[virtualRow.index].id, column.key)"
                      :type="column.type === 'number' || column.type === 'percentage' ? 'number' : 'text'"
                      :min="column.type === 'percentage' ? '0' : undefined"
                      :max="column.type === 'percentage' ? '100' : undefined"
                      :step="column.type === 'number' || column.type === 'percentage' ? '0.1' : undefined"
                      class="h-8"
                    />
                    <button
                      v-else
                      @click="startCellEdit(displayGrades[virtualRow.index].id, column.key)"
                      @keydown="(e) => handleKeyDown(e, displayGrades[virtualRow.index].id, column.key)"
                      :data-cell="`${displayGrades[virtualRow.index].id}-${column.key}`"
                      class="text-left hover:bg-gray-100 p-1 rounded w-full focus:outline-none focus:ring-2 focus:ring-primary"
                      :aria-label="`Edit ${column.label.toLowerCase()} for ${displayGrades[virtualRow.index].student_name}`"
                      tabindex="0"
                    >
                      {{ formatValue(displayGrades[virtualRow.index][column.key as keyof GradeEntry], column.type) }}
                    </button>
                  </template>

                  <!-- Status Column -->
                  <template v-else-if="column.key === 'status'">
                    <Badge :variant="getStatusVariant(displayGrades[virtualRow.index].status)" class="gap-1">
                      <component :is="getStatusIcon(displayGrades[virtualRow.index].status)" class="h-3 w-3" />
                      {{ displayGrades[virtualRow.index].status }}
                    </Badge>
                  </template>

                  <!-- Academic Integrity Column -->
                  <template v-else-if="column.key === 'academic_integrity'">
                    <span :class="getIntegrityColor(displayGrades[virtualRow.index].academic_integrity)">
                      {{ displayGrades[virtualRow.index].academic_integrity }}
                    </span>
                  </template>

                  <!-- Late Column -->
                  <template v-else-if="column.key === 'is_late'">
                    <Badge v-if="displayGrades[virtualRow.index].is_late" variant="destructive" class="gap-1">
                      <Clock class="h-3 w-3" />
                      Late
                    </Badge>
                    <span v-else class="text-muted-foreground">-</span>
                  </template>

                  <!-- Actions Column -->
                  <template v-else-if="column.key === 'actions'">
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal class="h-4 w-4" />
                    </Button>
                  </template>

                  <!-- Regular Columns -->
                  <template v-else>
                    {{ formatValue(displayGrades[virtualRow.index][column.key as keyof GradeEntry], column.type) }}
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- Loading State -->
    <div v-if="gradesLoading" class="flex items-center justify-center py-8">
      <LoadingSpinner size="lg" />
      <span class="ml-2 text-muted-foreground">Loading grades...</span>
    </div>

    <!-- Empty State -->
    <div v-else-if="displayGrades.length === 0" class="text-center py-8 text-muted-foreground">
      <FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
      <h3 class="text-lg font-medium mb-2">No grades found</h3>
      <p>No grades match your current filters.</p>
    </div>

    <!-- Bulk Grading Modal -->
    <BulkGradingModal
      :open="showBulkModal"
      :selected-grades="selectedGrades"
      :course-id="courseId"
      @update:open="showBulkModal = $event"
      @completed="handleBulkOperationCompleted"
      @cancelled="handleBulkOperationCancelled"
    />
  </div>
</template>

<style scoped>
/* Skip links for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Ensure proper scrolling behavior */
.overflow-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus indicators for accessibility */
button:focus,
input:focus,
[role='cell'] button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-b {
    border-width: 2px;
  }

  .hover\:bg-gray-50:hover {
    background-color: #e5e7eb;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
}
</style>
