<script setup lang="ts">
import { computed } from 'vue'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Progress } from '@/shared/components/ui/progress'
import LoadingSpinner from '@/shared/components/ui/LoadingSpinner.vue'
import type { OverviewStatistics } from '@/lecturer/types/models/assessment'
import {
  Users,
  FileText,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  BarChart3,
} from 'lucide-vue-next'

interface Props {
  data: OverviewStatistics | null
  loading?: boolean
  error?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: null,
})

// Computed properties for display
const enrollmentStats = computed(() => props.data?.statistics.enrollment_statistics)
const assessmentStructure = computed(() => props.data?.statistics.assessment_structure)
const scoreStats = computed(() => props.data?.statistics.score_statistics)
const completionStats = computed(() => props.data?.statistics.completion_statistics)

const completionPercentage = computed(() => {
  return completionStats.value ? Math.round(completionStats.value.overall_completion_rate * 100) : 0
})

const averageScorePercentage = computed(() => {
  return scoreStats.value ? Math.round(scoreStats.value.average_score) : 0
})

const weightCompletionStatus = computed(() => {
  if (!assessmentStructure.value) return { variant: 'secondary', text: 'Unknown' }
  
  if (assessmentStructure.value.weight_complete) {
    return { variant: 'default', text: 'Complete' }
  } else {
    return { variant: 'destructive', text: 'Incomplete' }
  }
})
</script>

<template>
  <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <!-- Loading State -->
    <template v-if="loading">
      <Card v-for="i in 4" :key="i" class="animate-pulse">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <div class="h-4 bg-gray-200 rounded w-20"></div>
          <div class="h-4 w-4 bg-gray-200 rounded"></div>
        </CardHeader>
        <CardContent>
          <div class="h-8 bg-gray-200 rounded w-16 mb-2"></div>
          <div class="h-3 bg-gray-200 rounded w-24"></div>
        </CardContent>
      </Card>
    </template>

    <!-- Error State -->
    <template v-else-if="error">
      <Card class="md:col-span-2 lg:col-span-4">
        <CardContent class="flex items-center justify-center py-8">
          <div class="text-center">
            <AlertTriangle class="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p class="text-red-600 font-medium">Failed to load statistics</p>
            <p class="text-sm text-muted-foreground">{{ error }}</p>
          </div>
        </CardContent>
      </Card>
    </template>

    <!-- Data Display -->
    <template v-else-if="data">
      <!-- Total Students -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Students</CardTitle>
          <Users class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ enrollmentStats?.total_enrolled_students || 0 }}</div>
          <p class="text-xs text-muted-foreground">
            {{ enrollmentStats?.active_students || 0 }} active
          </p>
        </CardContent>
      </Card>

      <!-- Assessment Structure -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Assessments</CardTitle>
          <FileText class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ assessmentStructure?.total_components || 0 }}</div>
          <div class="flex items-center gap-2 mt-1">
            <Badge :variant="weightCompletionStatus.variant" class="text-xs">
              {{ weightCompletionStatus.text }}
            </Badge>
            <span class="text-xs text-muted-foreground">
              {{ assessmentStructure?.total_weight || 0 }}% weight
            </span>
          </div>
        </CardContent>
      </Card>

      <!-- Average Score -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Average Score</CardTitle>
          <TrendingUp class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ averageScorePercentage }}%</div>
          <div class="mt-2">
            <Progress :value="averageScorePercentage" class="h-2" />
          </div>
          <p class="text-xs text-muted-foreground mt-1">
            {{ scoreStats?.total_graded_submissions || 0 }} submissions graded
          </p>
        </CardContent>
      </Card>

      <!-- Completion Rate -->
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Completion Rate</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ completionPercentage }}%</div>
          <div class="mt-2">
            <Progress :value="completionPercentage" class="h-2" />
          </div>
          <p class="text-xs text-muted-foreground mt-1">
            Overall completion rate
          </p>
        </CardContent>
      </Card>
    </template>

    <!-- No Data State -->
    <template v-else>
      <Card class="md:col-span-2 lg:col-span-4">
        <CardContent class="flex items-center justify-center py-8">
          <div class="text-center">
            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p class="text-muted-foreground">No statistics available</p>
          </div>
        </CardContent>
      </Card>
    </template>
  </div>
</template>
