import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useAssessmentA<PERSON> } from './useAssessmentApi'
import { useGradingAutoSave } from './useGradingAutoSave'
import type {
  Student,
  StudentGradingData,
  ComponentGradingData,
  Score,
  GradeUpdateRequest,
  BulkGradeUpdateRequest,
  BulkUpdateResponse,
  BulkPartialFailureResponse,
  GradeEntry,
  GradingSession,
  GradeFilter,
  GradeStatus,
  UpdateGradeRequest,
  BulkGradeUpdate,
  BulkOperationResult,
  ValidationResult,
} from '@/lecturer/types/models/assessment'
import { GradingMode } from '@/lecturer/types/models/assessment'

export interface GradingState {
  // API Data
  studentGradingData: StudentGradingData | null
  componentGradingData: ComponentGradingData | null

  // Legacy data for backward compatibility
  grades: GradeEntry[]
  students: Student[]

  // UI State
  currentMode: GradingMode
  selectedStudentId: number | null
  selectedAssessmentId: number | null
  selectedAssessmentDetailId: number | null

  // Changes tracking
  unsavedChanges: Map<number, GradeUpdateRequest>

  // Loading and error states
  loading: boolean
  error: string | null

  // Filtering and pagination
  filters: GradeFilter
  sortBy: string
  sortOrder: 'asc' | 'desc'
  currentPage: number
  itemsPerPage: number
  totalItems: number

  // Session management
  session: GradingSession | null
  lastSaved: Date | null
  validationErrors: Map<number, ValidationResult>
}

export function useGrading(courseOfferingId: number, assessmentId?: number) {
  // Local state
  const state = ref<GradingState>({
    // API Data
    studentGradingData: null,
    componentGradingData: null,

    // Legacy data for backward compatibility
    grades: [],
    students: [],

    // UI State
    currentMode: GradingMode.BY_STUDENT,
    selectedStudentId: null,
    selectedAssessmentId: assessmentId || null,
    selectedAssessmentDetailId: null,

    // Changes tracking
    unsavedChanges: new Map(),

    // Loading and error states
    loading: false,
    error: null,

    // Filtering and pagination
    filters: {
      status: undefined,
      is_late: undefined,
      is_excluded: undefined,
      has_academic_integrity_flag: undefined,
      score_range: undefined,
      submission_date_range: undefined,
      search_query: undefined,
    },
    sortBy: 'student_name',
    sortOrder: 'asc',
    currentPage: 1,
    itemsPerPage: 50,
    totalItems: 0,

    // Session management
    session: null,
    lastSaved: null,
    validationErrors: new Map(),
  })

  // API instance
  const api = useAssessmentApi()

  // Auto-save functionality
  const { startAutoSave, stopAutoSave, saveChanges, isSaving, lastAutoSave } = useGradingAutoSave(
    api,
    () => Array.from(state.value.unsavedChanges.values()),
    (savedGrades) => {
      // Update local state with saved grades
      savedGrades.forEach((savedGrade) => {
        const index = state.value.grades.findIndex((g) => g.id === savedGrade.id)
        if (index !== -1) {
          state.value.grades[index] = savedGrade
        }
      })
      // Clear saved changes from unsaved map
      savedGrades.forEach((savedGrade) => {
        state.value.unsavedChanges.delete(savedGrade.id)
      })
      state.value.lastSaved = new Date()
    },
  )

  // Computed properties
  const hasUnsavedChanges = computed(() => {
    return state.value.unsavedChanges.size > 0
  })

  const filteredGrades = computed(() => {
    let filtered = [...state.value.grades]

    // Apply filters
    if (state.value.filters.status && state.value.filters.status.length > 0) {
      filtered = filtered.filter((grade) => state.value.filters.status!.includes(grade.status))
    }

    if (state.value.filters.is_late !== undefined) {
      filtered = filtered.filter((grade) => grade.is_late === state.value.filters.is_late)
    }

    if (state.value.filters.is_excluded !== undefined) {
      filtered = filtered.filter((grade) => grade.is_excluded === state.value.filters.is_excluded)
    }

    if (state.value.filters.has_academic_integrity_flag !== undefined) {
      const hasFlag = state.value.filters.has_academic_integrity_flag
      filtered = filtered.filter((grade) => {
        const hasIntegrityFlag = grade.academic_integrity !== 'clear'
        return hasIntegrityFlag === hasFlag
      })
    }

    if (state.value.filters.score_range) {
      const { min, max } = state.value.filters.score_range
      filtered = filtered.filter((grade) => {
        return grade.percentage_score >= min && grade.percentage_score <= max
      })
    }

    if (state.value.filters.search_query) {
      const query = state.value.filters.search_query.toLowerCase()
      filtered = filtered.filter(
        (grade) =>
          grade.student_name.toLowerCase().includes(query) || grade.student_email.toLowerCase().includes(query),
      )
    }

    return filtered
  })

  const sortedGrades = computed(() => {
    const sorted = [...filteredGrades.value]

    sorted.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (state.value.sortBy) {
        case 'student_name':
          aValue = a.student_name
          bValue = b.student_name
          break
        case 'percentage_score':
          aValue = a.percentage_score
          bValue = b.percentage_score
          break
        case 'status':
          aValue = a.status
          bValue = b.status
          break
        case 'submission_date':
          aValue = a.submission_date ? new Date(a.submission_date) : new Date(0)
          bValue = b.submission_date ? new Date(b.submission_date) : new Date(0)
          break
        case 'graded_date':
          aValue = a.graded_date ? new Date(a.graded_date) : new Date(0)
          bValue = b.graded_date ? new Date(b.graded_date) : new Date(0)
          break
        default:
          aValue = a.student_name
          bValue = b.student_name
      }

      if (aValue < bValue) return state.value.sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return state.value.sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return sorted
  })

  const paginatedGrades = computed(() => {
    const start = (state.value.currentPage - 1) * state.value.itemsPerPage
    const end = start + state.value.itemsPerPage
    return sortedGrades.value.slice(start, end)
  })

  const totalPages = computed(() => {
    return Math.ceil(filteredGrades.value.length / state.value.itemsPerPage)
  })

  const currentStudent = computed(() => {
    if (!state.value.selectedStudentId) return null
    return state.value.students.find((s) => s.id === state.value.selectedStudentId) || null
  })

  const studentGrades = computed(() => {
    if (!state.value.selectedStudentId) return []
    return state.value.grades.filter((grade) => grade.student_id === state.value.selectedStudentId)
  })

  const gradesByAssessmentDetail = computed(() => {
    if (!state.value.selectedAssessmentDetailId) return []
    return state.value.grades.filter((grade) => grade.assessment_detail_id === state.value.selectedAssessmentDetailId)
  })

  // Internal helper functions
  const clearErrors = () => {
    state.value.error = null
    state.value.validationErrors.clear()
  }

  const setError = (error: string) => {
    state.value.error = error
    console.error('Grading Error:', error)
  }

  // New API Methods (using real endpoints)

  /**
   * Load student grading data using the real API endpoint
   */
  const loadStudentData = async (studentId: number): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.grading.getGradingDataByStudent(courseOfferingId, studentId)
      if (response.success && response.data) {
        state.value.studentGradingData = response.data
        state.value.selectedStudentId = studentId

        // Update legacy data for backward compatibility
        const student = response.data.student
        if (!state.value.students.find((s) => s.id === student.id)) {
          state.value.students.push(student)
        }
      } else {
        setError(response.message || 'Failed to load student grading data')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load student grading data')
    } finally {
      state.value.loading = false
    }
  }

  /**
   * Load component grading data using the real API endpoint
   */
  const loadComponentData = async (assessmentComponentId: number): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.grading.getGradingDataByComponent(courseOfferingId, assessmentComponentId)
      if (response.success && response.data) {
        state.value.componentGradingData = response.data
        state.value.selectedAssessmentId = assessmentComponentId

        // Update legacy data for backward compatibility
        const students: Student[] = []
        response.data.details.forEach((detail) => {
          detail.student_scores.forEach((studentScore) => {
            if (!students.find((s) => s.id === studentScore.student.id)) {
              students.push(studentScore.student)
            }
          })
        })
        state.value.students = students
      } else {
        setError(response.message || 'Failed to load component grading data')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load component grading data')
    } finally {
      state.value.loading = false
    }
  }

  /**
   * Update a single grade using the real API endpoint
   */
  const updateGradeReal = async (scoreId: number, gradeData: GradeUpdateRequest): Promise<void> => {
    try {
      const response = await api.grading.updateGrade(courseOfferingId, scoreId, gradeData)
      if (response.success && response.data) {
        // Update local state with the updated score
        if (state.value.studentGradingData) {
          // Update score in student grading data
          state.value.studentGradingData.assessments.forEach((assessment) => {
            assessment.details.forEach((detail) => {
              if (detail.score?.id === scoreId) {
                detail.score = response.data
              }
            })
          })
        }

        if (state.value.componentGradingData) {
          // Update score in component grading data
          state.value.componentGradingData.details.forEach((detail) => {
            detail.student_scores.forEach((studentScore) => {
              if (studentScore.score.id === scoreId) {
                studentScore.score = response.data
              }
            })
          })
        }

        // Remove from unsaved changes
        state.value.unsavedChanges.delete(scoreId)
        state.value.lastSaved = new Date()
      } else {
        setError(response.message || 'Failed to update grade')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update grade')
    }
  }

  /**
   * Bulk update grades using the real API endpoint
   */
  const bulkUpdateGradesReal = async (bulkData: BulkGradeUpdateRequest): Promise<void> => {
    try {
      const response = await api.grading.bulkUpdateGrades(courseOfferingId, bulkData)
      if (response.success && response.data) {
        // Handle successful bulk update
        if ('total_updated' in response.data) {
          // Success response
          const successData = response.data as BulkUpdateResponse
          console.log(`Successfully updated ${successData.total_updated} grades`)

          // Clear unsaved changes for successfully updated scores
          successData.updated_scores.forEach((result) => {
            state.value.unsavedChanges.delete(result.id)
          })

          // Refresh data to get updated scores
          if (state.value.currentMode === GradingMode.BY_STUDENT && state.value.selectedStudentId) {
            await loadStudentData(state.value.selectedStudentId)
          } else if (state.value.currentMode === GradingMode.BY_COMPONENT && state.value.selectedAssessmentId) {
            await loadComponentData(state.value.selectedAssessmentId)
          }
        } else {
          // Partial failure response
          const failureData = response.data as BulkPartialFailureResponse
          const errorMessage = `Bulk update partially failed: ${failureData.successfully_updated} succeeded, ${failureData.failed_updates} failed. Errors: ${failureData.bulk_errors.join(', ')}`
          setError(errorMessage)
        }

        state.value.lastSaved = new Date()
      } else {
        setError(response.message || 'Failed to bulk update grades')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to bulk update grades')
    }
  }

  // Legacy Data fetching methods (for backward compatibility)
  const fetchGrades = async (): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const params = {
        course_offering_id: courseOfferingId,
        assessment_id: state.value.selectedAssessmentId || undefined,
        page: state.value.currentPage,
        per_page: state.value.itemsPerPage,
        sort_by: state.value.sortBy,
        sort_order: state.value.sortOrder,
        ...state.value.filters,
      }

      const response = await api.grades.getAll(params)
      if (response.success && response.data) {
        state.value.grades = response.data
        state.value.totalItems = response.data.length // This should come from API pagination info

        // Extract unique students from grades
        const studentMap = new Map<number, Student>()
        response.data.forEach((grade) => {
          if (!studentMap.has(grade.student_id)) {
            studentMap.set(grade.student_id, {
              id: grade.student_id,
              name: grade.student_name,
              email: grade.student_email,
              student_number: '', // This should come from API
            })
          }
        })
        state.value.students = Array.from(studentMap.values())
      } else {
        setError(response.message || 'Failed to fetch grades')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch grades')
    } finally {
      state.value.loading = false
    }
  }

  const fetchGradesByStudent = async (studentId: number): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.grades.getByStudent(studentId, courseOfferingId)
      if (response.success && response.data) {
        // Update grades for this student
        const otherGrades = state.value.grades.filter((g) => g.student_id !== studentId)
        state.value.grades = [...otherGrades, ...response.data]
      } else {
        setError(response.message || 'Failed to fetch student grades')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch student grades')
    } finally {
      state.value.loading = false
    }
  }

  const fetchGradesByAssessment = async (assessmentId: number): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.grades.getByAssessment(assessmentId)
      if (response.success && response.data) {
        // Update grades for this assessment
        const otherGrades = state.value.grades.filter((g) => {
          // This would need assessment_id field in GradeEntry or another way to identify
          return true // Placeholder logic
        })
        state.value.grades = [...otherGrades, ...response.data]
      } else {
        setError(response.message || 'Failed to fetch assessment grades')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch assessment grades')
    } finally {
      state.value.loading = false
    }
  }

  // Grade operations
  const updateGrade = async (gradeId: number, updates: Partial<UpdateGradeRequest>): Promise<boolean> => {
    clearErrors()

    try {
      // Find the existing grade
      const existingGrade = state.value.grades.find((g) => g.id === gradeId)
      if (!existingGrade) {
        setError('Grade not found')
        return false
      }

      // Create update request
      const updateRequest: UpdateGradeRequest = {
        id: gradeId,
        ...updates,
      }

      // Validate the update
      const validationResponse = await api.grades.validate(updateRequest)
      if (validationResponse.success && validationResponse.data) {
        if (!validationResponse.data.is_valid) {
          state.value.validationErrors.set(gradeId, validationResponse.data)
          return false
        }
      }

      // Add to unsaved changes
      state.value.unsavedChanges.set(gradeId, updateRequest)

      // Update local state optimistically
      const gradeIndex = state.value.grades.findIndex((g) => g.id === gradeId)
      if (gradeIndex !== -1) {
        state.value.grades[gradeIndex] = {
          ...state.value.grades[gradeIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        }
      }

      // Clear validation errors for this grade
      state.value.validationErrors.delete(gradeId)

      return true
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update grade')
      return false
    }
  }

  const bulkUpdateGrades = async (updates: BulkGradeUpdate): Promise<BulkOperationResult | null> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.grades.bulkUpdate(updates)
      if (response.success && response.data) {
        // Update local state with successful updates
        response.data.updated_entries.forEach((updatedGrade) => {
          const index = state.value.grades.findIndex((g) => g.id === updatedGrade.id)
          if (index !== -1) {
            state.value.grades[index] = updatedGrade
          }
        })

        // Remove successful updates from unsaved changes
        response.data.updated_entries.forEach((grade) => {
          state.value.unsavedChanges.delete(grade.id)
        })

        state.value.lastSaved = new Date()
        return response.data
      } else {
        setError(response.message || 'Failed to bulk update grades')
        return null
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to bulk update grades')
      return null
    } finally {
      state.value.loading = false
    }
  }

  // Mode switching
  const switchMode = (mode: GradingMode): void => {
    state.value.currentMode = mode

    // Reset selections when switching modes
    if (mode === GradingMode.BY_STUDENT) {
      state.value.selectedAssessmentDetailId = null
    } else if (mode === GradingMode.BY_COMPONENT) {
      state.value.selectedStudentId = null
    }

    // Update session if exists
    if (state.value.session) {
      updateSession({ mode })
    }
  }

  // Student selection
  const selectStudent = (studentId: number): void => {
    state.value.selectedStudentId = studentId

    if (state.value.currentMode === GradingMode.BY_STUDENT) {
      fetchGradesByStudent(studentId)
    }

    // Update session
    if (state.value.session) {
      updateSession({ selected_student_id: studentId })
    }
  }

  const selectNextStudent = (): void => {
    if (!state.value.selectedStudentId || state.value.students.length === 0) return

    const currentIndex = state.value.students.findIndex((s) => s.id === state.value.selectedStudentId)
    const nextIndex = (currentIndex + 1) % state.value.students.length
    selectStudent(state.value.students[nextIndex].id)
  }

  const selectPreviousStudent = (): void => {
    if (!state.value.selectedStudentId || state.value.students.length === 0) return

    const currentIndex = state.value.students.findIndex((s) => s.id === state.value.selectedStudentId)
    const prevIndex = currentIndex === 0 ? state.value.students.length - 1 : currentIndex - 1
    selectStudent(state.value.students[prevIndex].id)
  }

  // Assessment selection
  const selectAssessment = (assessmentId: number): void => {
    state.value.selectedAssessmentId = assessmentId
    fetchGradesByAssessment(assessmentId)

    // Update session
    if (state.value.session) {
      updateSession({ selected_assessment_id: assessmentId })
    }
  }

  const selectAssessmentDetail = (assessmentDetailId: number): void => {
    state.value.selectedAssessmentDetailId = assessmentDetailId

    // Update session
    if (state.value.session) {
      updateSession({ selected_assessment_detail_id: assessmentDetailId })
    }
  }

  // Filtering and sorting
  const updateFilters = (newFilters: Partial<GradeFilter>): void => {
    state.value.filters = { ...state.value.filters, ...newFilters }
    state.value.currentPage = 1 // Reset to first page when filtering
  }

  const updateSort = (sortBy: string, sortOrder: 'asc' | 'desc' = 'asc'): void => {
    state.value.sortBy = sortBy
    state.value.sortOrder = sortOrder
    state.value.currentPage = 1 // Reset to first page when sorting
  }

  const clearFilters = (): void => {
    state.value.filters = {
      status: undefined,
      is_late: undefined,
      is_excluded: undefined,
      has_academic_integrity_flag: undefined,
      score_range: undefined,
      submission_date_range: undefined,
      search_query: undefined,
    }
    state.value.currentPage = 1
  }

  // Pagination
  const goToPage = (page: number): void => {
    if (page >= 1 && page <= totalPages.value) {
      state.value.currentPage = page
    }
  }

  const setItemsPerPage = (itemsPerPage: number): void => {
    state.value.itemsPerPage = itemsPerPage
    state.value.currentPage = 1 // Reset to first page
  }

  // Session management
  const createSession = async (): Promise<GradingSession | null> => {
    try {
      const sessionData = {
        course_offering_id: courseOfferingId,
        lecturer_id: 1, // This should come from auth
        mode: state.value.currentMode,
        selected_student_id: state.value.selectedStudentId || undefined,
        selected_assessment_id: state.value.selectedAssessmentId || undefined,
        selected_assessment_detail_id: state.value.selectedAssessmentDetailId || undefined,
        current_page: state.value.currentPage,
        items_per_page: state.value.itemsPerPage,
        sort_by: state.value.sortBy,
        sort_order: state.value.sortOrder,
        filters: state.value.filters,
        unsaved_changes: Array.from(state.value.unsavedChanges.values()).map((change) => ({
          ...change,
          // Convert to GradeEntry format for session storage
        })) as GradeEntry[],
        last_saved: state.value.lastSaved?.toISOString() || new Date().toISOString(),
        auto_save_enabled: true,
      }

      const response = await api.sessions.create(sessionData)
      if (response.success && response.data) {
        state.value.session = response.data
        return response.data
      }
      return null
    } catch (error) {
      console.error('Failed to create grading session:', error)
      return null
    }
  }

  const updateSession = async (updates: Partial<GradingSession>): Promise<void> => {
    if (!state.value.session) return

    try {
      const response = await api.sessions.update(state.value.session.id, updates)
      if (response.success && response.data) {
        state.value.session = response.data
      }
    } catch (error) {
      console.error('Failed to update grading session:', error)
    }
  }

  const loadSession = async (sessionId: string): Promise<boolean> => {
    try {
      const response = await api.sessions.get(sessionId)
      if (response.success && response.data) {
        const session = response.data
        state.value.session = session

        // Restore state from session
        state.value.currentMode = session.mode
        state.value.selectedStudentId = session.selected_student_id || null
        state.value.selectedAssessmentId = session.selected_assessment_id || null
        state.value.selectedAssessmentDetailId = session.selected_assessment_detail_id || null
        state.value.currentPage = session.current_page
        state.value.itemsPerPage = session.items_per_page
        state.value.sortBy = session.sort_by
        state.value.sortOrder = session.sort_order
        state.value.filters = session.filters

        // Restore unsaved changes
        session.unsaved_changes.forEach((change) => {
          const updateRequest: UpdateGradeRequest = {
            id: change.id,
            points_earned: change.points_earned,
            status: change.status,
            feedback: change.feedback,
            private_notes: change.private_notes,
            bonus_points: change.bonus_points,
            bonus_reason: change.bonus_reason,
            is_excluded: change.is_excluded,
            exclusion_reason: change.exclusion_reason,
            academic_integrity: change.academic_integrity,
            plagiarism_score: change.plagiarism_score,
            plagiarism_notes: change.plagiarism_notes,
            rubric_scores: change.rubric_scores,
          }
          state.value.unsavedChanges.set(change.id, updateRequest)
        })

        state.value.lastSaved = session.last_saved ? new Date(session.last_saved) : null

        // Fetch data based on restored state
        await fetchGrades()

        return true
      }
      return false
    } catch (error) {
      console.error('Failed to load grading session:', error)
      return false
    }
  }

  // Manual save
  const saveAllChanges = async (): Promise<boolean> => {
    if (state.value.unsavedChanges.size === 0) return true

    return await saveChanges()
  }

  // Utility functions
  const refreshData = async (): Promise<void> => {
    await fetchGrades()
  }

  const getGradeById = (id: number): GradeEntry | undefined => {
    return state.value.grades.find((grade) => grade.id === id)
  }

  const getGradesByStudent = (studentId: number): GradeEntry[] => {
    return state.value.grades.filter((grade) => grade.student_id === studentId)
  }

  const hasValidationErrors = (gradeId: number): boolean => {
    return state.value.validationErrors.has(gradeId)
  }

  const getValidationErrors = (gradeId: number): ValidationResult | undefined => {
    return state.value.validationErrors.get(gradeId)
  }

  // Cleanup function
  const cleanup = (): void => {
    stopAutoSave()
    state.value.grades = []
    state.value.students = []
    state.value.unsavedChanges.clear()
    state.value.validationErrors.clear()
    state.value.loading = false
    state.value.error = null
    state.value.session = null
    state.value.lastSaved = null
  }

  // Lifecycle management
  onMounted(async () => {
    await fetchGrades()
    startAutoSave()

    // Create session for tracking
    await createSession()
  })

  onUnmounted(() => {
    cleanup()
  })

  // Watch for changes to trigger auto-save
  watch(
    () => state.value.unsavedChanges.size,
    (newSize) => {
      if (newSize > 0 && state.value.session) {
        // Update session with current unsaved changes
        nextTick(() => {
          updateSession({
            unsaved_changes: Array.from(state.value.unsavedChanges.values()).map((change) => ({
              ...change,
              // Convert to GradeEntry format
            })) as GradeEntry[],
          })
        })
      }
    },
  )

  // Return readonly state and actions
  return {
    // State (readonly)
    grades: computed(() => state.value.grades),
    students: computed(() => state.value.students),
    studentGradingData: computed(() => state.value.studentGradingData),
    componentGradingData: computed(() => state.value.componentGradingData),
    currentMode: computed(() => state.value.currentMode),
    selectedStudentId: computed(() => state.value.selectedStudentId),
    selectedAssessmentId: computed(() => state.value.selectedAssessmentId),
    selectedAssessmentDetailId: computed(() => state.value.selectedAssessmentDetailId),
    loading: computed(() => state.value.loading),
    error: computed(() => state.value.error),
    filters: computed(() => state.value.filters),
    sortBy: computed(() => state.value.sortBy),
    sortOrder: computed(() => state.value.sortOrder),
    currentPage: computed(() => state.value.currentPage),
    itemsPerPage: computed(() => state.value.itemsPerPage),
    totalItems: computed(() => state.value.totalItems),
    session: computed(() => state.value.session),
    lastSaved: computed(() => state.value.lastSaved),

    // Computed properties
    hasUnsavedChanges,
    filteredGrades,
    sortedGrades,
    paginatedGrades,
    totalPages,
    currentStudent,
    studentGrades,
    gradesByAssessmentDetail,
    isSaving,
    lastAutoSave,

    // New API Methods (using real endpoints)
    loadStudentData,
    loadComponentData,
    updateGradeReal,
    bulkUpdateGradesReal,

    // Legacy Actions (for backward compatibility)
    fetchGrades,
    fetchGradesByStudent,
    fetchGradesByAssessment,
    updateGrade,
    bulkUpdateGrades,
    switchMode,
    selectStudent,
    selectNextStudent,
    selectPreviousStudent,
    selectAssessment,
    selectAssessmentDetail,
    updateFilters,
    updateSort,
    clearFilters,
    goToPage,
    setItemsPerPage,
    createSession,
    loadSession,
    saveAllChanges,
    refreshData,
    getGradeById,
    getGradesByStudent,
    hasValidationErrors,
    getValidationErrors,
    cleanup,
  }
}
