import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useAssessmentApi } from './useAssessmentApi'
import type {
  GradeStatistics,
  StudentPerformance,
  ExportOptions,
  ExportResult,
  ExportFormat,
  DateRange,
  GradeStatus,
  Assessment,
  OverviewStatistics,
  GradeMatrix,
  DetailedStatistics,
  ExcelExportFilters,
  PdfExportFilters,
} from '@/lecturer/types/models/assessment'

export interface ReportFilters {
  dateRange: DateRange | null
  assessmentTypes: string[]
  assessmentIds: number[]
  includeExcluded: boolean
  includePrivateNotes: boolean
  statusFilter: GradeStatus[]
  studentIds: number[]
  period: string // '7d', '30d', '90d', 'semester', 'all'
}

export interface ChartData {
  labels: string[]
  datasets: ChartDataset[]
}

export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  borderWidth?: number
  type?: string
}

export interface StatisticsSummary {
  totalStudents: number
  totalAssessments: number
  averageGrade: number
  medianGrade: number
  gradeDistribution: GradeDistributionData[]
  completionRate: number
  lateSubmissionRate: number
  academicIntegrityFlags: number
  atRiskStudents: number
  exceptionalStudents: number
}

export interface GradeDistributionData {
  range: string
  count: number
  percentage: number
  letterGrade: string
}

export interface PerformanceTrend {
  date: string
  averageScore: number
  completionRate: number
  submissionCount: number
}

export interface AssessmentComparison {
  assessmentId: number
  assessmentName: string
  averageScore: number
  medianScore: number
  standardDeviation: number
  completionRate: number
  difficulty: 'easy' | 'medium' | 'hard'
}

export interface ReportsState {
  // New API Data
  overviewStatistics: OverviewStatistics | null
  gradeMatrix: GradeMatrix | null
  detailedStatistics: DetailedStatistics | null

  // Legacy data for backward compatibility
  statistics: StatisticsSummary | null
  gradeStatistics: GradeStatistics[]
  studentPerformance: StudentPerformance[]
  assessments: Assessment[]
  chartData: {
    gradeDistribution: ChartData | null
    performanceTrends: ChartData | null
    assessmentComparison: ChartData | null
    completionRates: ChartData | null
  }
  filters: ReportFilters
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  exportStatus: {
    isExporting: boolean
    progress: number
    currentExport: ExportResult | null
  }
}

export function useAssessmentReports(courseOfferingId: number) {
  // Local state
  const state = ref<ReportsState>({
    // New API Data
    overviewStatistics: null,
    gradeMatrix: null,
    detailedStatistics: null,

    // Legacy data for backward compatibility
    statistics: null,
    gradeStatistics: [],
    studentPerformance: [],
    assessments: [],
    chartData: {
      gradeDistribution: null,
      performanceTrends: null,
      assessmentComparison: null,
      completionRates: null,
    },
    filters: {
      dateRange: null,
      assessmentTypes: [],
      assessmentIds: [],
      includeExcluded: false,
      includePrivateNotes: false,
      statusFilter: [],
      studentIds: [],
      period: '30d',
    },
    loading: false,
    error: null,
    lastUpdated: null,
    exportStatus: {
      isExporting: false,
      progress: 0,
      currentExport: null,
    },
  })

  // API instance
  const api = useAssessmentApi()

  // Computed properties
  const filteredStatistics = computed(() => {
    if (!state.value.statistics) return null

    // Apply filters to statistics if needed
    return state.value.statistics
  })

  const filteredStudentPerformance = computed(() => {
    let filtered = [...state.value.studentPerformance]

    // Apply student ID filter
    if (state.value.filters.studentIds.length > 0) {
      filtered = filtered.filter((student) => state.value.filters.studentIds.includes(student.student_id))
    }

    return filtered
  })

  const atRiskStudents = computed(() => {
    return state.value.studentPerformance.filter(
      (student) => student.risk_level === 'high' || student.risk_level === 'critical',
    )
  })

  const exceptionalStudents = computed(() => {
    return state.value.studentPerformance.filter((student) => student.overall_grade >= 85)
  })

  const hasData = computed(() => {
    return state.value.statistics !== null || state.value.gradeStatistics.length > 0
  })

  const isExporting = computed(() => {
    return state.value.exportStatus.isExporting
  })

  // Internal helper functions
  const clearErrors = () => {
    state.value.error = null
  }

  const setError = (error: string) => {
    state.value.error = error
    console.error('Assessment Reports Error:', error)
  }

  const updateLastModified = () => {
    state.value.lastUpdated = new Date()
  }

  // Chart data transformation functions
  const transformGradeDistributionData = (statistics: GradeStatistics[]): ChartData => {
    const allDistributions = statistics.flatMap((stat) => stat.grade_distribution)
    const gradeRanges = ['A', 'B', 'C', 'D', 'F']

    const data = gradeRanges.map((grade) => {
      const gradeData = allDistributions.filter((dist) => dist.letter_grade === grade)
      return gradeData.reduce((sum, dist) => sum + dist.count, 0)
    })

    return {
      labels: gradeRanges,
      datasets: [
        {
          label: 'Grade Distribution',
          data,
          backgroundColor: [
            '#10B981', // Green for A
            '#3B82F6', // Blue for B
            '#F59E0B', // Yellow for C
            '#EF4444', // Red for D
            '#6B7280', // Gray for F
          ],
          borderWidth: 1,
        },
      ],
    }
  }

  const transformPerformanceTrendsData = (trends: PerformanceTrend[]): ChartData => {
    return {
      labels: trends.map((trend) => new Date(trend.date).toLocaleDateString()),
      datasets: [
        {
          label: 'Average Score',
          data: trends.map((trend) => trend.averageScore),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          type: 'line',
        },
        {
          label: 'Completion Rate (%)',
          data: trends.map((trend) => trend.completionRate * 100),
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          type: 'line',
        },
      ],
    }
  }

  const transformAssessmentComparisonData = (comparisons: AssessmentComparison[]): ChartData => {
    return {
      labels: comparisons.map((comp) => comp.assessmentName),
      datasets: [
        {
          label: 'Average Score',
          data: comparisons.map((comp) => comp.averageScore),
          backgroundColor: '#3B82F6',
        },
        {
          label: 'Completion Rate (%)',
          data: comparisons.map((comp) => comp.completionRate * 100),
          backgroundColor: '#10B981',
        },
      ],
    }
  }

  const transformCompletionRatesData = (statistics: GradeStatistics[]): ChartData => {
    return {
      labels: statistics.map((stat) => stat.assessment_name),
      datasets: [
        {
          label: 'Completion Rate (%)',
          data: statistics.map((stat) => stat.completion_rate * 100),
          backgroundColor: statistics.map((stat) => {
            const rate = stat.completion_rate
            if (rate >= 0.9) return '#10B981' // Green
            if (rate >= 0.7) return '#F59E0B' // Yellow
            return '#EF4444' // Red
          }),
        },
      ],
    }
  }

  // New API Methods (using real endpoints)

  /**
   * Load overview statistics using the real API endpoint
   */
  const loadOverviewStatistics = async (): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.reporting.getOverviewStatistics(courseOfferingId)
      if (response.success && response.data) {
        state.value.overviewStatistics = response.data
        state.value.lastUpdated = new Date()
      } else {
        throw new Error(response.message || 'Failed to load overview statistics')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load overview statistics')
      throw error
    } finally {
      state.value.loading = false
    }
  }

  /**
   * Load grade matrix using the real API endpoint
   */
  const loadGradeMatrix = async (filters?: any): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.reporting.getGradeMatrix(courseOfferingId, filters)
      if (response.success && response.data) {
        state.value.gradeMatrix = response.data
        state.value.lastUpdated = new Date()
      } else {
        throw new Error(response.message || 'Failed to load grade matrix')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load grade matrix')
      throw error
    } finally {
      state.value.loading = false
    }
  }

  /**
   * Load detailed statistics using the real API endpoint
   */
  const loadDetailedStatistics = async (type?: string): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      const response = await api.reporting.getDetailedStatistics(courseOfferingId, type)
      if (response.success && response.data) {
        state.value.detailedStatistics = response.data
        state.value.lastUpdated = new Date()
      } else {
        throw new Error(response.message || 'Failed to load detailed statistics')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load detailed statistics')
      throw error
    } finally {
      state.value.loading = false
    }
  }

  /**
   * Export to Excel using the real API endpoint
   */
  const exportToExcel = async (filters?: ExcelExportFilters): Promise<void> => {
    state.value.exportStatus.isExporting = true
    clearErrors()

    try {
      const blob = await api.reporting.exportToExcel(courseOfferingId, filters)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `assessment-report-${courseOfferingId}-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      state.value.exportStatus.currentExport = {
        id: Date.now().toString(),
        filename: link.download,
        format: 'excel' as ExportFormat,
        status: 'completed',
        downloadUrl: url,
        createdAt: new Date().toISOString(),
        fileSize: blob.size,
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to export to Excel')
      throw error
    } finally {
      state.value.exportStatus.isExporting = false
    }
  }

  /**
   * Export to PDF using the real API endpoint
   */
  const exportToPdf = async (filters?: PdfExportFilters): Promise<void> => {
    state.value.exportStatus.isExporting = true
    clearErrors()

    try {
      const blob = await api.reporting.exportToPdf(courseOfferingId, filters)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `assessment-report-${courseOfferingId}-${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      state.value.exportStatus.currentExport = {
        id: Date.now().toString(),
        filename: link.download,
        format: 'pdf' as ExportFormat,
        status: 'completed',
        downloadUrl: url,
        createdAt: new Date().toISOString(),
        fileSize: blob.size,
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to export to PDF')
      throw error
    } finally {
      state.value.exportStatus.isExporting = false
    }
  }

  // Legacy Data fetching functions (for backward compatibility)
  const fetchStatistics = async (): Promise<void> => {
    try {
      // Fetch course analytics
      const analyticsResponse = await api.analytics.getCourseAnalytics(courseOfferingId, state.value.filters.period)
      if (analyticsResponse.success && analyticsResponse.data) {
        // Transform analytics data to statistics summary
        const analytics = analyticsResponse.data
        state.value.statistics = {
          totalStudents: analytics.total_students || 0,
          totalAssessments: analytics.total_assessments || 0,
          averageGrade: analytics.average_grade || 0,
          medianGrade: analytics.median_grade || 0,
          gradeDistribution: analytics.grade_distribution || [],
          completionRate: analytics.completion_rate || 0,
          lateSubmissionRate: analytics.late_submission_rate || 0,
          academicIntegrityFlags: analytics.academic_integrity_flags || 0,
          atRiskStudents: analytics.at_risk_students || 0,
          exceptionalStudents: analytics.exceptional_students || 0,
        }
      }

      // Fetch assessments for the course
      const assessmentsResponse = await api.assessments.getAll(courseOfferingId)
      if (assessmentsResponse.success && assessmentsResponse.data) {
        state.value.assessments = assessmentsResponse.data

        // Fetch statistics for each assessment
        const statisticsPromises = assessmentsResponse.data.map((assessment) =>
          api.assessments.getStatistics(assessment.id),
        )

        const statisticsResponses = await Promise.allSettled(statisticsPromises)
        state.value.gradeStatistics = statisticsResponses
          .filter(
            (response): response is PromiseFulfilledResult<any> =>
              response.status === 'fulfilled' && response.value.success,
          )
          .map((response) => response.value.data)
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to fetch statistics')
    }
  }

  const fetchStudentPerformance = async (): Promise<void> => {
    try {
      const response = await api.performance.getByCourse(courseOfferingId)
      if (response.success && response.data) {
        state.value.studentPerformance = response.data
      } else {
        throw new Error(response.message || 'Failed to fetch student performance')
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error('Failed to fetch student performance')
    }
  }

  const fetchPerformanceTrends = async (): Promise<PerformanceTrend[]> => {
    try {
      const response = await api.analytics.getPerformanceTrends(courseOfferingId, state.value.filters.period)
      if (response.success && response.data) {
        return response.data.trends || []
      }
      return []
    } catch (error) {
      console.error('Failed to fetch performance trends:', error)
      return []
    }
  }

  const fetchAssessmentComparison = async (): Promise<AssessmentComparison[]> => {
    try {
      const assessmentIds =
        state.value.filters.assessmentIds.length > 0
          ? state.value.filters.assessmentIds
          : state.value.assessments.map((a) => a.id)

      if (assessmentIds.length === 0) return []

      const response = await api.analytics.getAssessmentComparison(courseOfferingId, assessmentIds)
      if (response.success && response.data) {
        return response.data.comparisons || []
      }
      return []
    } catch (error) {
      console.error('Failed to fetch assessment comparison:', error)
      return []
    }
  }

  // Main data generation function
  const generateReport = async (): Promise<void> => {
    state.value.loading = true
    clearErrors()

    try {
      // Fetch all data in parallel
      await Promise.all([fetchStatistics(), fetchStudentPerformance()])

      // Generate chart data
      if (state.value.gradeStatistics.length > 0) {
        state.value.chartData.gradeDistribution = transformGradeDistributionData(state.value.gradeStatistics)
        state.value.chartData.completionRates = transformCompletionRatesData(state.value.gradeStatistics)
      }

      // Fetch and transform trends data
      const trends = await fetchPerformanceTrends()
      if (trends.length > 0) {
        state.value.chartData.performanceTrends = transformPerformanceTrendsData(trends)
      }

      // Fetch and transform comparison data
      const comparisons = await fetchAssessmentComparison()
      if (comparisons.length > 0) {
        state.value.chartData.assessmentComparison = transformAssessmentComparisonData(comparisons)
      }

      updateLastModified()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to generate report')
    } finally {
      state.value.loading = false
    }
  }

  // Filter management
  const updateFilters = (newFilters: Partial<ReportFilters>): void => {
    state.value.filters = { ...state.value.filters, ...newFilters }
  }

  const clearFilters = (): void => {
    state.value.filters = {
      dateRange: null,
      assessmentTypes: [],
      assessmentIds: [],
      includeExcluded: false,
      includePrivateNotes: false,
      statusFilter: [],
      studentIds: [],
      period: '30d',
    }
  }

  const applyFilters = (): void => {
    // Re-generate report with new filters
    generateReport()
  }

  // Export functionality
  const exportReport = async (
    format: ExportFormat,
    customOptions?: Partial<ExportOptions>,
  ): Promise<ExportResult | null> => {
    state.value.exportStatus.isExporting = true
    state.value.exportStatus.progress = 0
    clearErrors()

    try {
      const exportOptions: ExportOptions = {
        format,
        include_comments: true,
        include_statistics: true,
        include_rubric_scores: false,
        include_private_notes: state.value.filters.includePrivateNotes,
        date_range: state.value.filters.dateRange || undefined,
        status_filter: state.value.filters.statusFilter.length > 0 ? state.value.filters.statusFilter : undefined,
        student_filter: state.value.filters.studentIds.length > 0 ? state.value.filters.studentIds : undefined,
        assessment_filter: state.value.filters.assessmentIds.length > 0 ? state.value.filters.assessmentIds : undefined,
        ...customOptions,
      }

      // Start export
      const response = await api.export.exportData(courseOfferingId, exportOptions)
      if (response.success && response.data) {
        state.value.exportStatus.currentExport = response.data
        state.value.exportStatus.progress = 100
        return response.data
      } else {
        throw new Error(response.message || 'Failed to export report')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to export report')
      return null
    } finally {
      state.value.exportStatus.isExporting = false
    }
  }

  const exportAssessmentGrades = async (assessmentId: number, format: ExportFormat): Promise<ExportResult | null> => {
    state.value.exportStatus.isExporting = true
    clearErrors()

    try {
      const exportOptions: ExportOptions = {
        format,
        include_comments: true,
        include_statistics: false,
        include_rubric_scores: true,
        include_private_notes: state.value.filters.includePrivateNotes,
      }

      const response = await api.export.exportAssessmentGrades(assessmentId, exportOptions)
      if (response.success && response.data) {
        state.value.exportStatus.currentExport = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to export assessment grades')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to export assessment grades')
      return null
    } finally {
      state.value.exportStatus.isExporting = false
    }
  }

  const exportPerformanceReport = async (format: ExportFormat): Promise<ExportResult | null> => {
    state.value.exportStatus.isExporting = true
    clearErrors()

    try {
      const exportOptions: ExportOptions = {
        format,
        include_comments: false,
        include_statistics: true,
        include_rubric_scores: false,
        include_private_notes: false,
        student_filter: state.value.filters.studentIds.length > 0 ? state.value.filters.studentIds : undefined,
      }

      const response = await api.export.exportPerformanceReport(courseOfferingId, exportOptions)
      if (response.success && response.data) {
        state.value.exportStatus.currentExport = response.data
        return response.data
      } else {
        throw new Error(response.message || 'Failed to export performance report')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to export performance report')
      return null
    } finally {
      state.value.exportStatus.isExporting = false
    }
  }

  const downloadExportedFile = async (fileUrl: string): Promise<void> => {
    try {
      const blob = await api.export.downloadFile(fileUrl)

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = state.value.exportStatus.currentExport?.file_name || 'export.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to download file')
    }
  }

  // Utility functions
  const refreshData = async (): Promise<void> => {
    await generateReport()
  }

  const getAssessmentStatistics = (assessmentId: number): GradeStatistics | undefined => {
    return state.value.gradeStatistics.find((stat) => stat.assessment_id === assessmentId)
  }

  const getStudentPerformance = (studentId: number): StudentPerformance | undefined => {
    return state.value.studentPerformance.find((perf) => perf.student_id === studentId)
  }

  const calculateTrendDirection = (data: number[]): 'improving' | 'declining' | 'stable' => {
    if (data.length < 2) return 'stable'

    const recent = data.slice(-3) // Last 3 data points
    const earlier = data.slice(-6, -3) // Previous 3 data points

    if (recent.length === 0 || earlier.length === 0) return 'stable'

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, val) => sum + val, 0) / earlier.length

    const threshold = 2 // 2% threshold for stability
    const difference = ((recentAvg - earlierAvg) / earlierAvg) * 100

    if (difference > threshold) return 'improving'
    if (difference < -threshold) return 'declining'
    return 'stable'
  }

  // Cleanup function
  const cleanup = (): void => {
    state.value.statistics = null
    state.value.gradeStatistics = []
    state.value.studentPerformance = []
    state.value.assessments = []
    state.value.chartData = {
      gradeDistribution: null,
      performanceTrends: null,
      assessmentComparison: null,
      completionRates: null,
    }
    state.value.loading = false
    state.value.error = null
    state.value.lastUpdated = null
    state.value.exportStatus = {
      isExporting: false,
      progress: 0,
      currentExport: null,
    }
  }

  // Lifecycle management
  onMounted(() => {
    generateReport()
  })

  onUnmounted(() => {
    cleanup()
  })

  // Watch for filter changes to auto-refresh
  watch(
    () => state.value.filters,
    () => {
      // Debounce the report generation to avoid too many API calls
      setTimeout(() => {
        generateReport()
      }, 500)
    },
    { deep: true },
  )

  // Return readonly state and actions
  return {
    // New API State (readonly)
    overviewStatistics: computed(() => state.value.overviewStatistics),
    gradeMatrix: computed(() => state.value.gradeMatrix),
    detailedStatistics: computed(() => state.value.detailedStatistics),

    // Legacy State (readonly)
    statistics: computed(() => state.value.statistics),
    gradeStatistics: computed(() => state.value.gradeStatistics),
    studentPerformance: computed(() => state.value.studentPerformance),
    assessments: computed(() => state.value.assessments),
    chartData: computed(() => state.value.chartData),
    filters: computed(() => state.value.filters),
    loading: computed(() => state.value.loading),
    error: computed(() => state.value.error),
    lastUpdated: computed(() => state.value.lastUpdated),
    exportStatus: computed(() => state.value.exportStatus),

    // Computed properties
    filteredStatistics,
    filteredStudentPerformance,
    atRiskStudents,
    exceptionalStudents,
    hasData,
    isExporting,

    // New API Actions (using real endpoints)
    loadOverviewStatistics,
    loadGradeMatrix,
    loadDetailedStatistics,
    exportToExcel,
    exportToPdf,

    // Legacy Actions (for backward compatibility)
    generateReport,
    updateFilters,
    clearFilters,
    applyFilters,
    exportReport,
    exportAssessmentGrades,
    exportPerformanceReport,
    downloadExportedFile,
    refreshData,
    getAssessmentStatistics,
    getStudentPerformance,
    calculateTrendDirection,
    cleanup,
  }
}
