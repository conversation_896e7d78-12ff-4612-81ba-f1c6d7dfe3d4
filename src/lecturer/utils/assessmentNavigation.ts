/**
 * Navigation utilities for assessment module
 * Provides consistent route generation and navigation helpers
 */

import type { RouteLocationRaw } from 'vue-router'

export interface AssessmentRouteParams {
  courseOfferingId: number
  assessmentId?: number
  studentId?: number
}

export interface AssessmentNavigationOptions {
  replace?: boolean
  query?: Record<string, any>
  hash?: string
}

/**
 * Assessment route names matching the router configuration
 */
export const ASSESSMENT_ROUTES = {
  MANAGEMENT: 'lecturer-assessment-management',
  GRADING: 'lecturer-assessment-grading',
  GRADING_DETAIL: 'lecturer-assessment-grading-detail',
  REPORT: 'lecturer-assessment-report',
  GRADE_MATRIX: 'lecturer-grade-matrix',
} as const

export type AssessmentRouteName = typeof ASSESSMENT_ROUTES[keyof typeof ASSESSMENT_ROUTES]

/**
 * Generate route location for assessment management
 */
export function getAssessmentManagementRoute(
  courseOfferingId: number,
  options?: AssessmentNavigationOptions
): RouteLocationRaw {
  return {
    name: ASSESSMENT_ROUTES.MANAGEMENT,
    params: { courseOfferingId },
    query: options?.query,
    hash: options?.hash,
    replace: options?.replace,
  }
}

/**
 * Generate route location for assessment grading
 */
export function getAssessmentGradingRoute(
  courseOfferingId: number,
  assessmentId?: number,
  options?: AssessmentNavigationOptions
): RouteLocationRaw {
  const routeName = assessmentId ? ASSESSMENT_ROUTES.GRADING_DETAIL : ASSESSMENT_ROUTES.GRADING
  const params: any = { courseOfferingId }
  
  if (assessmentId) {
    params.assessmentId = assessmentId
  }

  return {
    name: routeName,
    params,
    query: options?.query,
    hash: options?.hash,
    replace: options?.replace,
  }
}

/**
 * Generate route location for assessment reports
 */
export function getAssessmentReportRoute(
  courseOfferingId: number,
  options?: AssessmentNavigationOptions
): RouteLocationRaw {
  return {
    name: ASSESSMENT_ROUTES.REPORT,
    params: { courseOfferingId },
    query: options?.query,
    hash: options?.hash,
    replace: options?.replace,
  }
}

/**
 * Generate route location for grade matrix
 */
export function getGradeMatrixRoute(
  courseOfferingId: number,
  options?: AssessmentNavigationOptions
): RouteLocationRaw {
  return {
    name: ASSESSMENT_ROUTES.GRADE_MATRIX,
    params: { courseOfferingId },
    query: options?.query,
    hash: options?.hash,
    replace: options?.replace,
  }
}

/**
 * Generate route location for courses list
 */
export function getCoursesRoute(): RouteLocationRaw {
  return {
    name: 'lecturer-courses',
  }
}

/**
 * Parse course offering ID from route params
 */
export function parseCourseOfferingId(params: any): number {
  const id = params.courseOfferingId
  if (typeof id === 'string') {
    return parseInt(id, 10)
  }
  if (typeof id === 'number') {
    return id
  }
  throw new Error(`Invalid courseOfferingId: ${id}`)
}

/**
 * Parse assessment ID from route params
 */
export function parseAssessmentId(params: any): number | undefined {
  const id = params.assessmentId
  if (!id) return undefined
  
  if (typeof id === 'string') {
    return parseInt(id, 10)
  }
  if (typeof id === 'number') {
    return id
  }
  throw new Error(`Invalid assessmentId: ${id}`)
}

/**
 * Parse student ID from route params
 */
export function parseStudentId(params: any): number | undefined {
  const id = params.studentId
  if (!id) return undefined
  
  if (typeof id === 'string') {
    return parseInt(id, 10)
  }
  if (typeof id === 'number') {
    return id
  }
  throw new Error(`Invalid studentId: ${id}`)
}

/**
 * Check if current route is an assessment route
 */
export function isAssessmentRoute(routeName?: string | null): boolean {
  if (!routeName) return false
  return Object.values(ASSESSMENT_ROUTES).includes(routeName as AssessmentRouteName)
}

/**
 * Get assessment tab from route name
 */
export function getAssessmentTabFromRoute(routeName?: string | null): string {
  if (!routeName) return 'management'
  
  switch (routeName) {
    case ASSESSMENT_ROUTES.MANAGEMENT:
      return 'management'
    case ASSESSMENT_ROUTES.GRADING:
    case ASSESSMENT_ROUTES.GRADING_DETAIL:
      return 'grading'
    case ASSESSMENT_ROUTES.GRADE_MATRIX:
      return 'grades'
    case ASSESSMENT_ROUTES.REPORT:
      return 'report'
    default:
      return 'management'
  }
}

/**
 * Generate breadcrumb items for assessment navigation
 */
export interface BreadcrumbItem {
  label: string
  route?: RouteLocationRaw
  active?: boolean
}

export function getAssessmentBreadcrumbs(
  courseOfferingId: number,
  currentRoute: string,
  assessmentId?: number
): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [
    {
      label: 'Courses',
      route: getCoursesRoute(),
    },
    {
      label: `Course ${courseOfferingId}`,
      route: getAssessmentManagementRoute(courseOfferingId),
    },
  ]

  switch (currentRoute) {
    case ASSESSMENT_ROUTES.MANAGEMENT:
      breadcrumbs.push({
        label: 'Assessment Management',
        active: true,
      })
      break
    case ASSESSMENT_ROUTES.GRADING:
      breadcrumbs.push({
        label: 'Grading',
        active: true,
      })
      break
    case ASSESSMENT_ROUTES.GRADING_DETAIL:
      breadcrumbs.push(
        {
          label: 'Grading',
          route: getAssessmentGradingRoute(courseOfferingId),
        },
        {
          label: `Assessment ${assessmentId}`,
          active: true,
        }
      )
      break
    case ASSESSMENT_ROUTES.REPORT:
      breadcrumbs.push({
        label: 'Reports & Analytics',
        active: true,
      })
      break
    case ASSESSMENT_ROUTES.GRADE_MATRIX:
      breadcrumbs.push({
        label: 'Grade Matrix',
        active: true,
      })
      break
  }

  return breadcrumbs
}

/**
 * Validate route parameters
 */
export function validateAssessmentRouteParams(params: AssessmentRouteParams): boolean {
  // Course offering ID is required and must be a positive integer
  if (!params.courseOfferingId || params.courseOfferingId <= 0) {
    return false
  }

  // Assessment ID, if provided, must be a positive integer
  if (params.assessmentId !== undefined && params.assessmentId <= 0) {
    return false
  }

  // Student ID, if provided, must be a positive integer
  if (params.studentId !== undefined && params.studentId <= 0) {
    return false
  }

  return true
}

/**
 * Generate URL-friendly assessment title
 */
export function generateAssessmentSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Assessment navigation helper composable
 */
export function useAssessmentNavigation() {
  return {
    // Route generators
    getAssessmentManagementRoute,
    getAssessmentGradingRoute,
    getAssessmentReportRoute,
    getGradeMatrixRoute,
    getCoursesRoute,
    
    // Parameter parsers
    parseCourseOfferingId,
    parseAssessmentId,
    parseStudentId,
    
    // Route utilities
    isAssessmentRoute,
    getAssessmentTabFromRoute,
    getAssessmentBreadcrumbs,
    validateAssessmentRouteParams,
    generateAssessmentSlug,
    
    // Constants
    ASSESSMENT_ROUTES,
  }
}
