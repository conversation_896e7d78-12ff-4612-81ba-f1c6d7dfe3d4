/**
 * Comprehensive error handling utilities for API responses
 */

export interface ApiError {
  code: string
  message: string
  details?: any
  statusCode?: number
  timestamp?: string
}

export interface ValidationError {
  field: string
  message: string
  code: string
  value?: any
}

export interface BulkOperationError {
  id: number
  error: string
  code: string
}

export interface ErrorContext {
  operation: string
  resource: string
  userId?: number
  courseId?: number
  timestamp: Date
}

/**
 * Standard error codes from the API documentation
 */
export enum ApiErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // Validation Errors
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  
  // Resource Errors
  NOT_FOUND = 'NOT_FOUND',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  
  // Grade-specific Errors
  GRADE_ALREADY_FINALIZED = 'GRADE_ALREADY_FINALIZED',
  INVALID_SCORE_RANGE = 'INVALID_SCORE_RANGE',
  ASSESSMENT_NOT_AVAILABLE = 'ASSESSMENT_NOT_AVAILABLE',
  STUDENT_NOT_ENROLLED = 'STUDENT_NOT_ENROLLED',
  
  // Bulk Operation Errors
  BULK_OPERATION_FAILED = 'BULK_OPERATION_FAILED',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  BATCH_SIZE_EXCEEDED = 'BATCH_SIZE_EXCEEDED',
  
  // System Errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Export Errors
  EXPORT_FAILED = 'EXPORT_FAILED',
  EXPORT_TOO_LARGE = 'EXPORT_TOO_LARGE',
  EXPORT_TIMEOUT = 'EXPORT_TIMEOUT',
}

/**
 * Parse API error response
 */
export function parseApiError(error: any, context?: ErrorContext): ApiError {
  // Handle different error response formats
  if (error?.response?.data) {
    const data = error.response.data
    
    return {
      code: data.error_code || data.code || 'UNKNOWN_ERROR',
      message: data.message || data.error || 'An unknown error occurred',
      details: data.details || data.errors,
      statusCode: error.response.status,
      timestamp: data.timestamp || new Date().toISOString(),
    }
  }
  
  // Handle network errors
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed. Please check your internet connection.',
      statusCode: 0,
      timestamp: new Date().toISOString(),
    }
  }
  
  // Handle timeout errors
  if (error?.code === 'TIMEOUT' || error?.message?.includes('timeout')) {
    return {
      code: 'REQUEST_TIMEOUT',
      message: 'Request timed out. Please try again.',
      statusCode: 408,
      timestamp: new Date().toISOString(),
    }
  }
  
  // Fallback for unknown errors
  return {
    code: 'UNKNOWN_ERROR',
    message: error?.message || 'An unexpected error occurred',
    statusCode: error?.status || 500,
    timestamp: new Date().toISOString(),
  }
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyMessage(error: ApiError, context?: ErrorContext): string {
  const errorMessages: Record<string, string> = {
    [ApiErrorCode.UNAUTHORIZED]: 'You are not authorized to perform this action. Please log in again.',
    [ApiErrorCode.FORBIDDEN]: 'You do not have permission to access this resource.',
    [ApiErrorCode.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
    
    [ApiErrorCode.VALIDATION_FAILED]: 'The provided data is invalid. Please check your input.',
    [ApiErrorCode.INVALID_INPUT]: 'Invalid input provided. Please check your data.',
    [ApiErrorCode.MISSING_REQUIRED_FIELD]: 'Required fields are missing. Please complete all required information.',
    
    [ApiErrorCode.NOT_FOUND]: 'The requested resource was not found.',
    [ApiErrorCode.RESOURCE_CONFLICT]: 'This action conflicts with the current state of the resource.',
    [ApiErrorCode.RESOURCE_LOCKED]: 'This resource is currently locked and cannot be modified.',
    
    [ApiErrorCode.GRADE_ALREADY_FINALIZED]: 'This grade has already been finalized and cannot be modified.',
    [ApiErrorCode.INVALID_SCORE_RANGE]: 'The score is outside the valid range for this assessment.',
    [ApiErrorCode.ASSESSMENT_NOT_AVAILABLE]: 'This assessment is not currently available for grading.',
    [ApiErrorCode.STUDENT_NOT_ENROLLED]: 'The student is not enrolled in this course.',
    
    [ApiErrorCode.BULK_OPERATION_FAILED]: 'The bulk operation failed. Please try again or contact support.',
    [ApiErrorCode.PARTIAL_SUCCESS]: 'Some operations completed successfully, but others failed.',
    [ApiErrorCode.BATCH_SIZE_EXCEEDED]: 'Too many items in batch. Please reduce the number of items.',
    
    [ApiErrorCode.INTERNAL_SERVER_ERROR]: 'A server error occurred. Please try again later.',
    [ApiErrorCode.SERVICE_UNAVAILABLE]: 'The service is temporarily unavailable. Please try again later.',
    [ApiErrorCode.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait a moment before trying again.',
    
    [ApiErrorCode.EXPORT_FAILED]: 'Export failed. Please try again or contact support.',
    [ApiErrorCode.EXPORT_TOO_LARGE]: 'Export file is too large. Please apply filters to reduce the data size.',
    [ApiErrorCode.EXPORT_TIMEOUT]: 'Export timed out. Please try again with a smaller dataset.',
  }
  
  return errorMessages[error.code] || error.message || 'An unexpected error occurred.'
}

/**
 * Check if error is recoverable
 */
export function isRecoverableError(error: ApiError): boolean {
  const recoverableErrors = [
    ApiErrorCode.RATE_LIMIT_EXCEEDED,
    ApiErrorCode.SERVICE_UNAVAILABLE,
    'NETWORK_ERROR',
    'REQUEST_TIMEOUT',
    ApiErrorCode.EXPORT_TIMEOUT,
  ]
  
  return recoverableErrors.includes(error.code as ApiErrorCode)
}

/**
 * Get retry delay for recoverable errors
 */
export function getRetryDelay(error: ApiError, attempt: number): number {
  const baseDelay = 1000 // 1 second
  const maxDelay = 30000 // 30 seconds
  
  switch (error.code) {
    case ApiErrorCode.RATE_LIMIT_EXCEEDED:
      return Math.min(baseDelay * Math.pow(2, attempt), maxDelay)
    case ApiErrorCode.SERVICE_UNAVAILABLE:
      return Math.min(baseDelay * attempt, maxDelay)
    case 'NETWORK_ERROR':
      return Math.min(baseDelay * Math.pow(1.5, attempt), maxDelay)
    default:
      return baseDelay * attempt
  }
}

/**
 * Format validation errors for display
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return ''
  
  if (errors.length === 1) {
    return `${errors[0].field}: ${errors[0].message}`
  }
  
  return errors.map(error => `• ${error.field}: ${error.message}`).join('\n')
}

/**
 * Format bulk operation errors
 */
export function formatBulkErrors(errors: BulkOperationError[]): string {
  if (errors.length === 0) return ''
  
  const grouped = errors.reduce((acc, error) => {
    if (!acc[error.code]) {
      acc[error.code] = []
    }
    acc[error.code].push(error)
    return acc
  }, {} as Record<string, BulkOperationError[]>)
  
  return Object.entries(grouped)
    .map(([code, errs]) => {
      const count = errs.length
      const sample = errs.slice(0, 3).map(e => `ID ${e.id}`).join(', ')
      const more = count > 3 ? ` and ${count - 3} more` : ''
      return `${code}: ${sample}${more}`
    })
    .join('\n')
}

/**
 * Log error for debugging/monitoring
 */
export function logError(error: ApiError, context?: ErrorContext): void {
  const logData = {
    error,
    context,
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: new Date().toISOString(),
  }
  
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', logData)
  }
  
  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
}

/**
 * Error handler composable for Vue components
 */
export function useErrorHandler() {
  const handleError = (error: any, context?: ErrorContext) => {
    const apiError = parseApiError(error, context)
    const userMessage = getUserFriendlyMessage(apiError, context)
    
    logError(apiError, context)
    
    return {
      apiError,
      userMessage,
      isRecoverable: isRecoverableError(apiError),
      retryDelay: isRecoverableError(apiError) ? getRetryDelay(apiError, 1) : 0,
    }
  }
  
  const handleValidationErrors = (errors: ValidationError[]) => {
    return formatValidationErrors(errors)
  }
  
  const handleBulkErrors = (errors: BulkOperationError[]) => {
    return formatBulkErrors(errors)
  }
  
  return {
    handleError,
    handleValidationErrors,
    handleBulkErrors,
    parseApiError,
    getUserFriendlyMessage,
    isRecoverableError,
  }
}

/**
 * Retry mechanism for recoverable errors
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  context?: ErrorContext
): Promise<T> {
  let lastError: ApiError | null = null
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      const apiError = parseApiError(error, context)
      lastError = apiError
      
      if (!isRecoverableError(apiError) || attempt === maxAttempts) {
        throw apiError
      }
      
      const delay = getRetryDelay(apiError, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}
