/**
 * Utility functions for handling file exports and downloads
 */

export interface ExportResult {
  success: boolean
  filename: string
  size: number
  downloadUrl?: string
  error?: string
}

export interface ExportOptions {
  filename?: string
  mimeType?: string
  autoDownload?: boolean
}

/**
 * Download a blob as a file
 */
export function downloadBlob(
  blob: Blob,
  filename: string,
  options: ExportOptions = {}
): ExportResult {
  try {
    const url = window.URL.createObjectURL(blob)
    
    if (options.autoDownload !== false) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
    
    return {
      success: true,
      filename,
      size: blob.size,
      downloadUrl: url,
    }
  } catch (error) {
    console.error('Failed to download blob:', error)
    return {
      success: false,
      filename,
      size: 0,
      error: error instanceof Error ? error.message : 'Download failed',
    }
  }
}

/**
 * Generate a filename with timestamp
 */
export function generateFilename(
  prefix: string,
  extension: string,
  includeTimestamp: boolean = true
): string {
  const timestamp = includeTimestamp 
    ? new Date().toISOString().split('T')[0] 
    : ''
  
  const parts = [prefix, timestamp].filter(Boolean)
  return `${parts.join('-')}.${extension}`
}

/**
 * Format file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Get MIME type for common export formats
 */
export function getMimeType(format: string): string {
  const mimeTypes: Record<string, string> = {
    'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'pdf': 'application/pdf',
    'csv': 'text/csv',
    'json': 'application/json',
    'xml': 'application/xml',
  }
  
  return mimeTypes[format.toLowerCase()] || 'application/octet-stream'
}

/**
 * Validate blob before download
 */
export function validateBlob(blob: Blob, expectedMimeType?: string): boolean {
  if (!blob || blob.size === 0) {
    return false
  }
  
  if (expectedMimeType && blob.type !== expectedMimeType) {
    console.warn(`Expected MIME type ${expectedMimeType}, got ${blob.type}`)
  }
  
  return true
}

/**
 * Create a download link element
 */
export function createDownloadLink(
  url: string,
  filename: string,
  options: { 
    className?: string
    text?: string
    autoClick?: boolean
  } = {}
): HTMLAnchorElement {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  
  if (options.className) {
    link.className = options.className
  }
  
  if (options.text) {
    link.textContent = options.text
  }
  
  if (options.autoClick) {
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
  
  return link
}

/**
 * Clean up blob URLs to prevent memory leaks
 */
export function cleanupBlobUrl(url: string): void {
  try {
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.warn('Failed to revoke blob URL:', error)
  }
}

/**
 * Export data as JSON file
 */
export function exportAsJson(
  data: any,
  filename: string,
  options: ExportOptions = {}
): ExportResult {
  try {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    
    return downloadBlob(blob, filename, options)
  } catch (error) {
    return {
      success: false,
      filename,
      size: 0,
      error: error instanceof Error ? error.message : 'JSON export failed',
    }
  }
}

/**
 * Export data as CSV file
 */
export function exportAsCsv(
  data: any[],
  filename: string,
  options: ExportOptions & { headers?: string[] } = {}
): ExportResult {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error('Data must be a non-empty array')
    }
    
    const headers = options.headers || Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV values
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv' })
    
    return downloadBlob(blob, filename, options)
  } catch (error) {
    return {
      success: false,
      filename,
      size: 0,
      error: error instanceof Error ? error.message : 'CSV export failed',
    }
  }
}

/**
 * Check if browser supports file downloads
 */
export function supportsDownload(): boolean {
  return typeof window !== 'undefined' && 
         'URL' in window && 
         'createObjectURL' in window.URL &&
         'revokeObjectURL' in window.URL
}

/**
 * Progress tracking for large exports
 */
export class ExportProgressTracker {
  private callbacks: Array<(progress: number) => void> = []
  private _progress = 0
  
  get progress(): number {
    return this._progress
  }
  
  setProgress(value: number): void {
    this._progress = Math.max(0, Math.min(100, value))
    this.callbacks.forEach(callback => callback(this._progress))
  }
  
  onProgress(callback: (progress: number) => void): void {
    this.callbacks.push(callback)
  }
  
  reset(): void {
    this._progress = 0
    this.callbacks.forEach(callback => callback(0))
  }
  
  complete(): void {
    this.setProgress(100)
  }
}

/**
 * Batch export utility for multiple files
 */
export async function batchExport(
  exports: Array<{
    data: any
    filename: string
    format: 'json' | 'csv' | 'blob'
    blob?: Blob
  }>,
  progressCallback?: (progress: number) => void
): Promise<ExportResult[]> {
  const results: ExportResult[] = []
  
  for (let i = 0; i < exports.length; i++) {
    const exportItem = exports[i]
    
    let result: ExportResult
    
    switch (exportItem.format) {
      case 'json':
        result = exportAsJson(exportItem.data, exportItem.filename)
        break
      case 'csv':
        result = exportAsCsv(exportItem.data, exportItem.filename)
        break
      case 'blob':
        if (!exportItem.blob) {
          result = {
            success: false,
            filename: exportItem.filename,
            size: 0,
            error: 'Blob is required for blob format',
          }
        } else {
          result = downloadBlob(exportItem.blob, exportItem.filename)
        }
        break
      default:
        result = {
          success: false,
          filename: exportItem.filename,
          size: 0,
          error: `Unsupported format: ${exportItem.format}`,
        }
    }
    
    results.push(result)
    
    if (progressCallback) {
      progressCallback(((i + 1) / exports.length) * 100)
    }
  }
  
  return results
}
