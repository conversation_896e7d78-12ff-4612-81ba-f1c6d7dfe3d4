{"env": {"CLAUDE_CODE_MAX_OUTPUT_TOKENS": "30000"}, "permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(pnpm type-check:*)", "Bash(pnpm lint:oxlint:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(gh pr create:*)", "Bash(pnpm build:*)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npx vue-tsc:*)", "Bash(pnpm add:*)", "mcp__browser-tools__getConsoleLogs", "mcp__browser-tools__getConsoleErrors", "mcp__browser-tools__getNetworkErrors"], "deny": []}}